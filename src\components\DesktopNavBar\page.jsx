import { Fragment, useRef, useState } from "react";
import { Popover, Transition, Menu } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { useEffect } from "react";
import { signOut } from "next-auth/react";
import { ConfirmModal } from "../ConfirmModal ";
import { AlertModal } from "../AlertModal";
export default function DesktopNavBar({
  Sports,
  userExist,
  navigation,
  pathname,
}) {
  const popperButtonRef = useRef();
  const [playerObject, setPlayerObject] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const closePopover = () => {
    if (popperButtonRef.current) {
      popperButtonRef.current.click();
    }
  };

  const handleLogout = () => {
    if (localStorage.getItem("Booked")) localStorage.removeItem("Booked");
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch(`/api/logout`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result) {
          signOut({ callbackUrl: "/login" });
        }
      })
      .catch((error) => {
        console.log("error 34");
      });
  };
  const handleDeleteAccount = () => {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };

    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.user) {
          const myHeaders = new Headers();
          myHeaders.append("Authorization", `Bearer ${result.user.token}`);

          const requestOptions = {
            method: "DELETE",
            headers: myHeaders,
            redirect: "follow",
          };

          fetch(`/api/delete/`, requestOptions)
            .then((response) =>
              response
                .json()
                .then((data) => ({ status: response.status, data }))
            )
            .then(({ status, data }) => {
              if (status === 200) {
                setAlertMessage(data.message);
                setShowAlertModal(true);
                signOut({ callbackUrl: "/login" });
                handleLogout();
              } else {
                setAlertMessage(data.error || "Failed to delete account");
                setShowAlertModal(true);
              }
            })
            .catch((error) => {
              console.error(error);
              setAlertMessage("An unexpected error occurred");
              setShowAlertModal(true);
            });
        }
      })
      .catch((error) => {
        console.log("error 69");
        setAlertMessage("An unexpected error occurred");
        setShowAlertModal(true);
      });
  };

  const confirmDeleteAccount = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleConfirmDelete = () => {
    handleDeleteAccount();
    setShowModal(false);
  };
  const handleCloseAlertModal = () => {
    setShowAlertModal(false);
  };
  useEffect(() => {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.user) {
          const myHeaders = new Headers();
          myHeaders.append("Authorization", `Bearer ${result.user.token}`);

          const requestOptions = {
            method: "GET",
            headers: myHeaders,
            redirect: "follow",
          };

          fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${result.user.player_id}`,
            requestOptions
          )
            .then((response) => response.json())
            .then((result) => setPlayerObject(result))
            .catch((error) => console.error(error));
        }
      })
      .catch((error) => {
        console.log("error 69");
      });
  }, [pathname]);

  if (typeof window !== "undefined") {
    document?.querySelector("body")?.addEventListener("click", (event) => {
      if (event.target.closest("nav")) return;
      if (document.querySelector('nav [data-headlessui-state="open"] button'))
        document
          .querySelector('nav [data-headlessui-state="open"] button')
          .click();
    });
  }
  return (
    <div className="hidden lg:flex lg:gap-x-12 items-center">
      <Popover className="relative">
        <Popover.Button
          ref={popperButtonRef}
          className="flex items-center gap-x-1 text-base font-semibold leading-6 text-gray-900"
        >
          Sports
          <ChevronDownIcon
            className="h-5 w-5 flex-none text-gray-400"
            aria-hidden="true"
          />
        </Popover.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-200"
          enterFrom="opacity-0 translate-y-1"
          enterTo="opacity-100 translate-y-0"
          leave="transition ease-in duration-150"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 translate-y-1"
        >
          <Popover.Panel className="headerDropdown absolute -left-8 top-full z-10 mt-3 w-max rounded-[20px] bg-white shadow-lg ring-1 ring-gray-900/5">
            <div className="my-2 py-2 px-4 max-h-[60vh] overflow-y-auto">
              {Sports &&
                Sports?.map((item, index) => (
                  <div
                    key={index}
                    className="group relative flex items-center gap-x-6 rounded-lg p-4 pr-10 text-base leading-6 hover:bg-gray-50"
                    onClick={closePopover}
                  >
                    <div className="flex-auto">
                      <Link
                        href={`/categories/${item.handle.toLowerCase()}?category=${item.name.toLowerCase()}`}
                        className="flex font-semibold text-gray-900 flex-row"
                      >
                        <img
                          class="w-8 h-8 rounded-full mr-3"
                          src={item.image}
                          alt="Rounded avatar"
                        />
                        {item.name}
                        <span className="absolute inset-0" />
                      </Link>
                      {/* <p className="mt-1 text-gray-600">{item.description}</p> */}
                    </div>
                  </div>
                ))}
            </div>
          </Popover.Panel>
        </Transition>
      </Popover>
      {navigation.map((item) => (
        <Link
          target="_blank"
          key={item.name}
          href={item.href}
          className="text-base font-semibold leading-6 text-gray-900"
        >
          {item.name}
        </Link>
      ))}
      {userExist ? (
        <Menu as="div" className="relative">
          <Menu.Button className="-m-1.5 flex items-center">
            <span className="hidden lg:flex lg:items-center">
              <span
                className="ml-4 text-md font-semibold leading-6 text-gray-900"
                aria-hidden="true"
              >
                {playerObject.firstName} {playerObject.lastName}
              </span>
              <ChevronDownIcon
                className="ml-2 h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </Menu.Button>
          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="absolute right-0 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none z-[9999]">
              <Menu.Item>
                <Link
                  href="/account"
                  className="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100"
                >
                  Account
                </Link>
              </Menu.Item>
              <Menu.Item>
                <div
                  onClick={handleLogout}
                  className="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100"
                >
                  Logout
                </div>
              </Menu.Item>
              <Menu.Item>
                {/* <div
                  onClick={confirmDeleteAccount}
                  className="block px-3 py-1 text-sm leading-6 text-red-500 hover:bg-gray-100"
                >
                  Forget Me
                </div> */}
                <Link
                  href="/delete_account"
                  className="block px-3 py-1 text-sm leading-6 text-red-500 hover:bg-gray-100"
                >
                  Forget Me
                </Link>
              </Menu.Item>
            </Menu.Items>
          </Transition>
        </Menu>
      ) : (
        <Link
          href="/login"
          className="text-base font-semibold leading-6 px-6 py-2 bg-black text-white rounded-md"
        >
          Sign In
        </Link>
      )}
      <ConfirmModal
        show={showModal}
        onClose={handleCloseModal}
        onConfirm={handleConfirmDelete}
      />
      <AlertModal
        show={showAlertModal}
        onClose={handleCloseAlertModal}
        message={alertMessage}
      />
    </div>
  );
}
