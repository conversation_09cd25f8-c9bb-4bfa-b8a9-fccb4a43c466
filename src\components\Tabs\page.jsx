"use client";
import { useState } from "react";

const tabs = [
  { name: "Popular Sports" },
  { name: "Team Sports" },
  { name: "Racquet Sports" },
  { name: "Fitness Sports" },
  { name: "Recreation Sports" },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Tabs() {
  const [currentTab, setCurrentTab] = useState("Popular Sports");

  const handleCurrentTab = (value) => {
    setCurrentTab(value);
  };

  return (
    <div className="mt-2">
      <div className="">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex overflow-x-auto lg:overflow-x-hidden space-x-8" aria-label="Tabs">
            {tabs.map((tab, index) => (
              <div
                key={index}
                className={classNames(
                  tab.name === currentTab
                    ? "border-indigo-500 text-indigo-600"
                    : "border-transparent text-gray-500 hover:border-gray-200 hover:text-gray-700",
                  "flex whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium cursor-pointer"
                )}
                onClick={() => handleCurrentTab(tab.name)}
              >
                {tab.name}
              </div>
            ))}
          </nav>
        </div>
      </div>
    </div>
  );
}
