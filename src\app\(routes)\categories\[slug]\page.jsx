import Filters from "@/components/Categories/Filters/page"
const courseMeta = {
  title:'KhelCoach Courses',
  description:'This is a Courses Page Test Description',
  image:`/MainLogo.svg`,
  url: process.env.NEXT_PUBLIC_BASE_URL,
}

export const metadata = {
  title: {
    template: "%s - Khel Coach",
    default: courseMeta.title,
  },
  description: courseMeta.description,
  openGraph: {
    title: courseMeta.title,
    description: courseMeta.description,
    url: courseMeta.url,
    locale: "en-US",
    siteName: courseMeta.title,
    type: "website",
    images: [
      {
        url: courseMeta.image,
      },
    ],
  },
  twitter: {
    title: courseMeta.title,
    description: courseMeta.description,
    images: courseMeta.image,
    card: "summary_large_image",
  },
  alternates: {
    canonical: courseMeta.url,
  },
  metadataBase: process.env.NEXT_PUBLIC_BASE_URL
};

export default function Categories() {
  return (
    <div className="w-full">
      <div><Filters /></div>
    </div>
  )
}
