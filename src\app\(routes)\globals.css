@tailwind base;
@tailwind components;
@tailwind utilities;

.enableLi li {
  list-style: disc;
  margin-top: 10px;
}
.enableLi ol {
  padding-left: 20px;
}
.testimonialSec {
  background: linear-gradient(
    273.03deg,
    rgba(0, 174, 239, 0.99) 0.66%,
    #69b8d3 29.86%,
    #bdc0bc 100%
  );
}
.react-multiple-carousel__arrow--left {
  left: auto !important;
  right: 65px;
  top: -54px;
  min-width: 35px !important;
  min-height: 35px !important;
}
.react-multiple-carousel__arrow--right {
  right: 18px !important;
  top: -54px;
  min-width: 35px !important;
  min-height: 35px !important;
}
.react-multiple-carousel__arrow::before {
  font-size: 20px;
  font-weight: 800;
  color: #000 !important;
}
.react-multiple-carousel__arrow {
  background: transparent !important;
  border: 2px solid !important;
}

ul.react-multi-carousel-track li > div {
  margin-right: 20px;
}
.react-multiple-carousel__arrow:hover {
  background: #00aeef !important;
  border-color: #00aeef !important;
}
.react-multiple-carousel__arrow:hover.react-multiple-carousel__arrow::before {
  color: #fff !important;
}
.react-multi-carousel-list {
  overflow-x: clip !important;
  overflow-y: unset !important;
}
.react-multi-carousel-dot button {
  width: 52px !important;
  height: auto !important;
  border-radius: 100px !important;
  border-width: 2px !important;
  border-color: #000 !important;
  margin-right: 20px !important;
}
.react-multi-carousel-dot-list {
  bottom: -3.5rem !important;
}

li.react-multi-carousel-dot.react-multi-carousel-dot--active button {
  border-color: #fff !important;
}

.productdescriptions li{
  list-style: disc;
  padding: 2px 0;
}

@media screen and (max-width: 750px) {
  .react-multiple-carousel__arrow--left,
  .react-multiple-carousel__arrow--right {
    display: none !important;
  }
}

/* width */
.headerDropdown ::-webkit-scrollbar {
  width: 5px;
}

/* Track */
.headerDropdown ::-webkit-scrollbar-track {
  /* box-shadow: inset 0 0 5px grey;  */
  border-radius: auto;
}

/* Handle */
.headerDropdown ::-webkit-scrollbar-thumb {
  background: darkgrey;
  border-radius: 10px;
}

/* Handle on hover */
.headerDropdown ::-webkit-scrollbar-thumb:hover {
  background: darkgrey;
}
input:focus,
input:focus-visible,
input:focus-visible,
input:focus-within {
  outline: none;
}
.tooltip {
  position: relative;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 100%;
  background-color: #d2d2d2;
  color: #5c5555;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 0%;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}
