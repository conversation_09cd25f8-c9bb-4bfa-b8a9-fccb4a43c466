import React from "react";
import Image from "next/image";

const ShareModal = ({ isOpen, onClose }) => {
    const handleShare = (platform) => {
        let url = "";
        const currentPageUrl = encodeURIComponent(window.location.href);
        switch (platform) {
          case "facebook":
            url = `https://www.facebook.com/sharer/sharer.php?u=${currentPageUrl}`;
            break;
          case "gmail":
            url = `mailto:?subject=Check%20out%20this%20link&body=${currentPageUrl}`;
            break;
          case "instagram":
            url = `https://www.instagram.com/?url=${currentPageUrl}`;
            break;
          case "whatsapp":
            url = `https://api.whatsapp.com/send?text=Check%20out%20this%20link:%20${currentPageUrl}`;
            break;
          default:
            break;
        }
        window.open(url, "_blank");
      };

  return (
    <div className={`modal ${isOpen ? "block" : "hidden"} fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-10`}>
      <div className="modal-content bg-white w-96 p-8 rounded-lg text-center flex flex-col gap-6">
        <h2 className="text-xl font-bold">Share with your friends</h2>
        <div className="flex justify-between items-center">
        <Image priority src='/ShareFacebook.svg' alt='facebook' height={50} width={50} onClick={() => handleShare("facebook")} className="cursor-pointer"/>
        <Image src='/ShareGoogle.svg' alt='gmail' height={50} width={50} onClick={() => handleShare("gmail")} className="cursor-pointer"/>
        <Image src='/ShareInstagram.svg' alt='instagram' height={50} width={50} onClick={() => handleShare("instagram")} className="cursor-pointer"/>
        <Image src='/ShareWhatsapp.svg' alt='whatsapp' height={50} width={50} onClick={() => handleShare("whatsapp")} className="cursor-pointer"/>
        </div>
        <button onClick={onClose} className="bg-gray-300 text-gray-800 py-2 px-4 rounded-lg mt-4">Close</button>
      </div>
    </div>
  );
};

export default ShareModal;