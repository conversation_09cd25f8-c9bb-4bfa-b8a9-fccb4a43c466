import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const BookingModal = ({
  data,
  isModalOpen,
  setIsModalOpen,
  bookingArray,
  setBookingArray,
}) => {
  console.log(data, "course");
  const [totalPrice, setTotalPrice] = useState("");
  const container = useRef(null);
  // const containerNav = useRef(null);
  const containerOffset = useRef(null);
  const [eventsTime, setEventsTime] = useState([]);
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [selectedOption, setSelectedOption] = useState(null);
  // const [alreadyBooked, setAlreadyBooked] = useState();
  const [availableSlots, setAvailableSlots] = useState();
  const [availableDays, setAvailableDays] = useState();
  const [selectedPlusSessionTime, setSelectedPlusSessionTime] = useState();
  const [plusOneDate, setPlusOneDate] = useState(true);

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setSelectedPlusSessionTime(false);
    if (option == "30 mins") {
      setTotalPrice(`₹ ${data?.fees?.fees30}`);
    } else if (option === "45 mins") {
      setTotalPrice(`₹ ${data?.fees?.fees45}`);
    } else if (option === "60 mins") {
      setTotalPrice(`₹ ${data?.fees?.fees60}`);
    }
  };

  const calculateEvents = (event) => {
    let hours = new Date(event?.start).getHours() * 12 + 2;
    let minutes = new Date(event?.start).getMinutes() * 0.2;
    let start = Math.round(hours + minutes);
    let timeDifference = Math.abs(
      new Date(event?.end) - new Date(event?.start)
    );
    let end = timeDifference / (1000 * 60 * 60);
    end = end * 12;
    return { start, end };
  };

  const handleTimeValue = (value) => {
    setTime(value);
    setSelectedPlusSessionTime(false);
  };

  const dayOfWeekMap = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const handleDateValue = (date) => {
    setSelectedPlusSessionTime(false);
    let value;
    if (plusOneDate) {
      value = new Date(date.getTime() + 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];
      setDate(value);
      setPlusOneDate(false);
    } else {
      value = date.toISOString().split("T")[0];
      setDate(value);
    }
    setAvailableSlots(data.dates);
    const selectedDate = new Date(value);
    const selectedDayString = dayOfWeekMap[selectedDate.getDay()];
    const isAllowedDate = data.dates.days.includes(selectedDayString);
    setAvailableDays(isAllowedDate);
    if (isAllowedDate) {
      let requestOptions = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        redirect: "follow",
      };
      fetch("/api/profile", requestOptions)
        .then((response) => response.json())
        .then((result) => {
          if (result?.user?.token) {
            const token = result.user.token;

            const myHeaders = new Headers();
            myHeaders.append("Content-Type", "application/json");
            myHeaders.append("Authorization", `Bearer ${token}`);

            const raw = JSON.stringify({
              coachId: data?.coach_id?._id,
              startDate: `${value}T00:00:00`,
              endDate: `${value}T23:59:00`,
              courseId: data?._id,
            });

            const requestOptions = {
              method: "POST",
              headers: myHeaders,
              body: raw,
              redirect: "follow",
            };
            fetch(
              `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/events`,
              requestOptions
            )
              .then((response) => response.json())
              .then((result) => {
                const events = result
                  ? result.map((event) => {
                      const startDate = new Date(date);
                      const startTime = event.startTime;
                      const [startHour, startMinute] = startTime
                        .split(":")
                        .map(Number);
                      startDate.setHours(startHour, startMinute, 0, 0);

                      const endDate = new Date(date);
                      const endTime = event.endTime;
                      const [endHour, endMinute] = endTime
                        .split(":")
                        .map(Number);
                      endDate.setHours(endHour, endMinute, 0, 0);

                      return {
                        start: startDate,
                        end: endDate,
                      };
                    })
                  : null;
                setEventsTime(events);
              })
              .catch((error) => console.error(error));
          }
        })
        .catch((error) => console.error(error));
    }
  };

  const handleSave = () => {
    const newTime = time.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
    const [hours, minutes] = newTime.split(":").map(Number);
    const startTime = new Date(date);
    startTime.setHours(hours, minutes);
    const sessionMinutes = parseInt(selectedOption);
    const endTime = new Date(startTime.getTime() + sessionMinutes * 60000);
    const endCalenderTime = new Date(`${date} ${data?.dates?.endTime}`);
    const isExceedingEndTime = endTime.getTime() > endCalenderTime.getTime();
    if (isExceedingEndTime) {
      setSelectedPlusSessionTime("Selected time exceeds coach's end time");
      return;
    }
    const startTimeStr = startTime.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    const endTimeStr = endTime.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    if (eventsTime) {
      for (const event of eventsTime) {
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        if (
          (startTime >= eventStart && startTime < eventEnd) ||
          (endTime > eventStart && endTime <= eventEnd) ||
          (startTime < eventStart && endTime > eventEnd)
        ) {
          setSelectedPlusSessionTime(
            "Selected time overlaps with an existing event"
          );
          return;
        }
      }
    }
    for (const bookedSession of bookingArray) {
      let [startTimePart, endTimePart] = bookedSession.time.split("to");
      const [startHour, startMinute] = startTimePart.trim().split(":");
      const [endHour, endMinute] = endTimePart.trim().split(":");
      const sessionStartDate = new Date(bookedSession.date);
      const sessionEndDate = new Date(bookedSession.date);

      if (navigator.platform == "MacIntel") {
        sessionStartDate.setHours(
          parseInt(startHour),
          parseInt(startMinute),
          0,
          0
        );
        sessionEndDate.setHours(parseInt(endHour), parseInt(endMinute), 0, 0);
      } else {
        let startComponents = startTimePart.split(":");
        let startHour = parseInt(startComponents[0]);
        let startMinute = parseInt(startComponents[1].split(" ")[0]);

        if (startComponents[1].includes("PM") && startHour < 12) {
          startHour += 12;
        } else if (startComponents[1].includes("AM") && startHour === 12) {
          startHour -= 12;
        }

        startTimePart = `${startHour.toString().padStart(2, "0")}:${startMinute
          .toString()
          .padStart(2, "0")}`;

        let endComponents = endTimePart.split(":");
        let endHour = parseInt(endComponents[0]);
        let endMinute = parseInt(endComponents[1].split(" ")[0]);

        if (endComponents[1].includes("PM") && endHour < 12) {
          endHour += 12;
        } else if (endComponents[1].includes("AM") && endHour === 12) {
          endHour -= 12;
        }
        endTimePart = `${endHour.toString().padStart(2, "0")}:${endMinute
          .toString()
          .padStart(2, "0")}`;
        sessionStartDate.setHours(
          parseInt(startTimePart),
          parseInt(startMinute),
          0,
          0
        );
        sessionEndDate.setHours(
          parseInt(endTimePart),
          parseInt(endMinute),
          0,
          0
        );
      }
      if (
        (date === bookedSession.date &&
          endTime > sessionStartDate &&
          endTime <= sessionEndDate) ||
        (startTime < sessionStartDate && sessionEndDate < endTime)
      ) {
        setSelectedPlusSessionTime(
          "Time slot overlaps with an existing booking."
        );
        return;
      }
    }

    const d = new Date(date);
    const ds = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dow = ds[d?.getDay()];
    const newBooking = {
      session: selectedOption,
      date: date,
      time: `${startTimeStr} to ${endTimeStr}`,
      name: data.courseName,
      price: totalPrice,
      day: dow,
    };
    setBookingArray([...bookingArray, newBooking]);
    setIsModalOpen(!isModalOpen);
    document.body.classList.remove("overflow-hidden");
  };

  const handleClose = (isModalOpen) => {
    setIsModalOpen(!isModalOpen);
    document.body.classList.remove("overflow-hidden");
  };

  function convertTo24HourFormat(time12h) {
    const [time, period] = time12h.split(" ");
    const [hours, minutes] = time.split(":");
    let hours24h = parseInt(hours, 10);

    if (period === "PM" && hours24h !== 12) {
      hours24h += 12;
    } else if (period === "AM" && hours24h === 12) {
      hours24h = 0;
    }
    return `${hours24h.toString().padStart(2, "0")}:${minutes}`;
  }

  const disabledTimes = bookingArray.flatMap((item) => {
    const startTime = item.time.split(" to ")[0].trim();
    const endTime = item.time.split(" to ")[1].trim();
    const startTime24h = convertTo24HourFormat(startTime);
    const endTime24h = convertTo24HourFormat(endTime);
    const [startHour, startMinute] = startTime24h.split(":").map(Number);
    const [endHour, endMinute] = endTime24h.split(":").map(Number);
    const disabledTimesInRange = [];
    for (let hour = startHour; hour <= endHour; hour++) {
      for (
        let minute = hour === startHour ? startMinute : 0;
        minute <= (hour === endHour ? endMinute - 1 : 59);
        minute++
      ) {
        disabledTimesInRange.push(new Date().setHours(hour, minute));
      }
    }
    return { date: item.date, times: disabledTimesInRange };
  });
  const generateDisabledTimes = () => {
    const disabledTimes = [];

    for (const event of eventsTime) {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      // Add each 30-minute interval within the event's start and end times to the disabledTimes array
      for (
        let i = eventStart;
        i < eventEnd;
        i = new Date(i.getTime() + 30 * 60000)
      ) {
        disabledTimes.push(new Date(i));
      }
    }

    return disabledTimes;
  };

  const disableEndTime = () => {
    const startTime = data.dates.startTime;
    const endTime = data.dates.endTime;

    // Function to convert "HH:mm" time strings to Date objects
    const parseTime = (time) => {
      const [hours, minutes] = time.split(":").map(Number);
      const now = new Date();
      return new Date(now.setHours(hours, minutes, 0, 0));
    };

    const eventEnd = parseTime(endTime);

    // Return an array with only the end time disabled
    return [eventEnd];
  };

  return (
    <div className={`fixed z-10 inset-0 ${isModalOpen ? "block" : "hidden"}`}>
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>
        <div
          className={`h-[90vh] inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left shadow-xl transform transition-all sm:my-8 lg:my-0 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 overflow-y-auto`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <Image
            src="/close.png"
            alt="closeButton"
            width={50}
            height={50}
            onClick={handleClose}
            className="absolute top-4 right-4 w-5 cursor-pointer"
          />
          <div>
            <div className="flex h-full flex-col w-full">
              <header className="flex border-b px-6 py-4 w-full">
                <div className="flex flex-col gap-5 w-full">
                  <div className="flex flex-col gap-2">
                    <p>Select Date</p>
                    <DatePicker
                      onKeyDown={(e) => e.preventDefault()}
                      selected={date}
                      onChange={(value) => handleDateValue(value)}
                      minDate={
                        new Date(
                          Math.max(
                            new Date(
                              new Date(data?.dates?.startDate).getUTCFullYear(),
                              new Date(data?.dates?.startDate).getUTCMonth(),
                              new Date(data?.dates?.startDate).getUTCDate()
                            ),
                            new Date()
                          )
                        )
                      }
                      maxDate={new Date(data?.dates?.endDate)}
                      dateFormat="dd-MM-yyyy"
                      placeholderText="Select a date"
                      className="px-3 py-2 block w-full rounded-md border-0 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                      filterDate={(date) => {
                        const dayOfWeek = [
                          "Sun",
                          "Mon",
                          "Tue",
                          "Wed",
                          "Thu",
                          "Fri",
                          "Sat",
                        ];
                        const day = dayOfWeek[date.getDay()];
                        return data?.dates?.days?.includes(day);
                      }}
                    />

                    {!availableDays ? (
                      <p className="text-sky-600 text-sm">
                        Coach Only Taking Sessions on{" "}
                        {data.dates.days.join("-")}
                      </p>
                    ) : (
                      availableSlots && (
                        <p className="text-sky-600 text-sm">
                          The available time slots are between{" "}
                          {`${
                            parseInt(data?.dates?.startTime.slice(0, 2)) > 12
                              ? parseInt(data?.dates?.startTime.slice(0, 2)) -
                                12
                              : data?.dates?.startTime.slice(0, 2)
                          }:${data?.dates?.startTime.slice(3, 5)} ${
                            parseInt(data?.dates?.startTime.slice(0, 2)) >= 12
                              ? "PM"
                              : "AM"
                          }`}{" "}
                          to{" "}
                          {`${
                            parseInt(data?.dates?.endTime.slice(0, 2)) > 12
                              ? parseInt(data?.dates?.endTime.slice(0, 2)) - 12
                              : data?.dates?.endTime.slice(0, 2)
                          }:${data?.dates?.endTime.slice(3, 5)} ${
                            parseInt(data?.dates?.endTime.slice(0, 2)) >= 12
                              ? "PM"
                              : "AM"
                          }`}
                        </p>
                      )
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    {console.log(disabledTimes, "opopop", availableDays)}
                    <p>Time</p>
                    <DatePicker
                      onKeyDown={(e) => e.preventDefault()}
                      selected={time}
                      onChange={(time) => handleTimeValue(time)}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={30}
                      dateFormat="h:mm aa"
                      minTime={
                        date && new Date(date).toDateString() === new Date().toDateString() 
                          ? new Date() 
                          : new Date().setHours(
                              data?.dates?.startTime?.split(":")[0],
                              data?.dates?.startTime?.split(":")[1]
                            )
                      }
                      maxTime={new Date().setHours(
                        data?.dates?.endTime?.split(":")[0],
                        data?.dates?.endTime?.split(":")[1]
                      )}
                      placeholderText="Start Time"
                      excludeTimes={[
                        ...generateDisabledTimes(),
                        ...disableEndTime(),
                        ...(disabledTimes
                          .filter((item) => item.date === date)
                          .flatMap((item) => item.times) || []),
                      ]}
                      disabled={!availableDays}
                      className="px-3 py-2 block w-full rounded-md border-0 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                    />
                    {selectedPlusSessionTime && (
                      <p className="text-red-600 text-sm">
                        {selectedPlusSessionTime}
                      </p>
                    )}
                  </div>
                  <div className="flex justify-between">
                    {["30 mins", "45 mins", "60 mins"].map((option) => {
                      const fee =
                        data?.fees[`fees${option.replace(" mins", "")}`];
                      const isDisabled = fee <= 0;

                      return (
                        <div
                          key={option}
                          className={`border border-black px-3 sm:px-5 md:px-7 py-1 rounded-lg text-sm md:text-base ${
                            isDisabled
                              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                              : "hover:border-white hover:bg-sky-400 hover:text-white hover:cursor-pointer"
                          } ${
                            selectedOption === option
                              ? "bg-sky-400 text-white"
                              : ""
                          }`}
                          onClick={() =>
                            !isDisabled && handleOptionSelect(option)
                          }
                          style={{ opacity: isDisabled ? 0.5 : 1 }}
                        >
                          <p>{option}</p>
                          <p>{isDisabled ? "₹ N/A" : `₹ ${Math.round(fee)}`}</p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </header>
              <div className="isolate flex flex-auto overflow-hidden bg-white h-[38vh] md:h-[34vh] lg:h-[38vh] 2xl:h-[42vh]">
                <div
                  ref={container}
                  className="flex flex-auto flex-col overflow-auto"
                >
                  <div className="flex w-full flex-auto">
                    <div className="w-14 flex-none bg-white ring-1 ring-gray-100" />
                    <div className="grid flex-auto grid-cols-1 grid-rows-1">
                      <div
                        className="col-start-1 col-end-2 row-start-1 grid divide-y divide-gray-100"
                        style={{
                          gridTemplateRows: "repeat(48, minmax(3.5rem, 1fr))",
                        }}
                      >
                        <div
                          ref={containerOffset}
                          className="row-end-1 h-7"
                        ></div>
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            12AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            1AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            2AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            3AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            4AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            5AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            6AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            7AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            8AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            9AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            10AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            11AM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            12PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            1PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            2PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            3PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            4PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            5PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            6PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            7PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            8PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            9PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            10PM
                          </div>
                        </div>
                        <div />
                        <div>
                          <div className="sticky left-0 -ml-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                            11PM
                          </div>
                        </div>
                        <div />
                      </div>

                      {/* Events */}
                      <ol
                        className="col-start-1 col-end-2 row-start-1 grid grid-cols-1 ml-3"
                        style={{
                          gridTemplateRows:
                            "1.75rem repeat(288, minmax(0, 1fr)) auto",
                          width: "fit-content",
                          columnGap: "10px",
                        }}
                      >
                        {eventsTime?.length > 0 &&
                          eventsTime?.map((x, i) => {
                            return (
                              <li
                                key={`key${i}`}
                                className="relative mt-px flex"
                                style={{
                                  gridRow: `${
                                    calculateEvents(x).start
                                  } / span ${calculateEvents(x).end}`,
                                }}
                              >
                                <div className="group inset-1 flex flex-col overflow-y-auto rounded-lg bg-gray-200 p-2 text-xs leading-5 hover:bg-gray-300">
                                  <p
                                    className="order-1 font-semibold text-gray-700"
                                    style={{ zIndex: i === 0 ? 1 : i + 1 }}
                                  >
                                    Booked
                                  </p>
                                  <p className="text-gray-500 group-hover:text-gray-700">
                                    <time dateTime={x?.start}>
                                      {`${new Date(x?.start).toLocaleString(
                                        "en-IN",
                                        {
                                          hour: "numeric",
                                          minute: "numeric",
                                          hour12: true,
                                        }
                                      )} - 
                                    ${new Date(x?.end).toLocaleString("en-IN", {
                                      hour: "numeric",
                                      minute: "numeric",
                                      hour12: true,
                                    })}`}
                                    </time>
                                  </p>
                                </div>
                              </li>
                            );
                          })}
                      </ol>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-row justify-between items-center mt-4">
            <div className="text-base">Total: {totalPrice}</div>
            <div className="flex gap-8">
              <button
                className="bg-red-500 text-white px-4 py-2 rounded-lg"
                onClick={handleSave}
                disabled={
                  !time ||
                  !selectedOption ||
                  !availableDays ||
                  selectedPlusSessionTime
                }
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
