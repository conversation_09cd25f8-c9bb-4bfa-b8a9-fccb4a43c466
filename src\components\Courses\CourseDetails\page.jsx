"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import BookingModal from "../../BookingModal";
import CourseCoach from "./CourseCoach/page";
import ShareModal from "./ShareModal/page";
import Timings from "./Timings/page";
import { useRouter, useParams } from "next/navigation";
import RazorPay from "@/components/RazorPay/page";
import ReactStars from "react-rating-stars-component";

export default function CourseDetails({ data }) {
  const router = useRouter();
  const params = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [bookingArray, setBookingArray] = useState([]);
  const [playerData, setPlayerData] = useState({});
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const handleBookings = () => {
    localStorage.setItem("Booked", params.handle);
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.user) {
          setIsModalOpen(true);
        } else {
          router.push("/login");
        }
      })
      .catch((error) => {
        console.log("error 39");
      });
  };

  const handleShareModal = () => {
    setIsShareModalOpen(true);
  };

  const closeShareModal = () => {
    setIsShareModalOpen(false);
  };

  useEffect(() => {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        setIsLoggedIn(result?.user ? true : false);
        if (result.user) {
          const myHeaders = new Headers();
          myHeaders.append("Authorization", `Bearer ${result.user.token}`);

          const requestOptions = {
            method: "GET",
            headers: myHeaders,
            redirect: "follow",
          };

          fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${result.user.player_id}`,
            requestOptions
          )
            .then((response) => response.json())
            .then((result) => setPlayerData(result))
            .catch((error) => console.error(error));
        }
      })
      .catch((error) => console.error(error));
  }, []);

  let total = 0;
  for (const booking of bookingArray) {
    const price = Number(booking.price.replace(/[^\d.]/g, ""));
    total += price;
  }
  const platformFee = data.academy_id?.platformFee
    ? data.academy_id.platformFee / 100
    : 0.12;

  const platformTax = data.classType === "course"
    ? data.fees.feesCourse * platformFee
    : total * platformFee;

  // let platformTax = data.classType === "course" ? data.fees.feesCourse * 0.12 : total * 0.12;
  let gst = platformTax * 0.18;
  let coachGst = data?.coach_id?.hasGst
    ? data.classType === "course"
      ? data.fees.feesCourse * 0.18
      : total * 0.18
    : 0;
  let finalPrice = Math.ceil(
    (data.classType === "course" ? data.fees.feesCourse : total) +
    platformTax +
    gst +
    coachGst
  );
  useEffect(() => {
    if (isModalOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isModalOpen]);

  return (
    <div className="md:flex">
      <div className="w-full md:w-1/2">
        <h2 className="text-3xl text-black not-italic">
          {/* Youth Basketball: How to Get Better at Basketball For Kids */}
          {data?.courseName}
        </h2>
        <div className="mt-6 flex items-center gap-2">
          {/* <Image
            priority
            src="/review.svg"
            alt="arrowsvg"
            width={100}
            height={100}
            className="w-1/4 h-auto"
          /> */}
          <ReactStars
            count={5}
            value={data?.ratings?.stars}
            edit={false}
            size={25}
            color="#E5E4E2"
            activeColor="#FCAB00"
          />
          <p>{`${data?.ratings ? data?.ratings?.noOfRatings : 0} Ratings`} </p>
        </div>
        <div className="mt-6">
          <Image
            priority
            src={data?.images[0]?.url}
            alt="course"
            width={500}
            height={500}
            className="w-full h-[400px] md:h-[540px] object-cover rounded"
          />
        </div>
      </div>
      <div className="w-full md:w-1/2 md:flex flex-col">
        <div className="flex flex-col md:flex-row justify-end gap-6 md:gap-4 mt-6 md:mt-0">
          <div
            className={`border text-base p-6 w-80 md:w-72 m-auto md:m-0 rounded-lg flex flex-col justify-between ${data?.classType == "course" ? "gap-0" : "gap-2"
              }`}
          >
            <h3>
              {data?.fees?.feesCourse ? `${(data.fees.feesCourse + platformTax).toFixed(2)} + ${gst.toFixed(2)} (18% GST)`
                : total
                  ? `${(total + platformTax).toFixed(2)} + ${(gst + coachGst).toFixed(2)} (18% GST)`
                  : `Starts from ₹${(
                    data?.fees?.fees +
                    data?.fees?.fees * platformTax
                  ).toFixed(2)} + 18% GST`}
            </h3>

            {data.classType === "course" ? (
              <RazorPay
                data={data}
                playerData={playerData}
                bookingArray={bookingArray}
                finalPrice={finalPrice}
                setBookingArray={setBookingArray}
              />
            ) : bookingArray.length > 0 ? (
              <RazorPay
                data={data}
                bookingArray={bookingArray}
                setBookingArray={setBookingArray}
                finalPrice={finalPrice}
                playerData={playerData}
              />
            ) : (
              <button
                className="w-full py-3.5 px-8 bg-red-300 text-white rounded-lg"
                disabled={true}
              >
                Add Slots
              </button>
            )}
          </div>

          <div className="border text-base p-6 w-80 md:w-72 m-auto md:m-0 rounded-lg flex flex-col justify-between">
            <h3>Share with your friends</h3>
            <button
              onClick={handleShareModal}
              className="w-full py-3.5 px-8 bg-sky-500 text-white rounded-lg"
            >
              SHARE
            </button>
            {isShareModalOpen && (
              <ShareModal isOpen={isShareModalOpen} onClose={closeShareModal} />
            )}
          </div>
        </div>
        <div className="ml-4 mt-6 flex flex-col gap-6">
          <Timings
            data={data}
            handleBookings={handleBookings}
            bookingArray={bookingArray}
            setBookingArray={setBookingArray}
            isLoggedIn={isLoggedIn}
          />
        </div>
        <div className="ml-4 mt-6 flex flex-col justify-between">
          <div className="border text-base rounded-lg p-6 flex flex-col justify-between">
            <div className="flex items-center gap-6">
              <div className="flex flex-row gap-2 md:items-center">
                <p className="whitespace-nowrap text-[15px]">Location:</p>
                <p className="text-[15px]">
                  {data.facility.addressLine1}, {data.facility.state}
                </p>
              </div>
              {data?.facility?.location?.coordinates[0] &&
                data?.facility?.location?.coordinates[1] && (
                  <Link
                    href={`https://www.google.com/maps/search/?api=1&query=${data?.facility?.location?.coordinates[0]},${data?.facility?.location?.coordinates[1]}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sky-500 whitespace-nowrap text-[14px]"
                  >
                    Show on map
                  </Link>
                )}
            </div>
          </div>
        </div>
        <div className="ml-4 mt-6 flex flex-col gap-6">
          <div className="border text-base rounded-lg p-6 flex flex-col justify-between">
            <CourseCoach data={data} />
          </div>
        </div>
        {/* Modal */}
        {isModalOpen && (
          <BookingModal
            bookingArray={bookingArray}
            setBookingArray={setBookingArray}
            data={data}
            isModalOpen={isModalOpen}
            setIsModalOpen={setIsModalOpen}
          />
        )}
      </div>
    </div>
  );
}
