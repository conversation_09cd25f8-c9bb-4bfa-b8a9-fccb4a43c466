import Image from "next/image";
import React, { useEffect, useState } from "react";
import Booking from "./Bookings/page";
import Transactions from "./Transactions/page";

// Reusable filter button component
const FilterButton = ({ onClick, active, children }) => {
  return (
    <span
      onClick={onClick}
      className={`ease-linear duration-200 border border-solid border-darkgray p-1 cursor-pointer pb-1 px-4 rounded-md ${active ? 'bg-black text-white' : 'bg-white text-black'}`}
    >
      {children}
    </span>
  );
};

export default function RightDetails({ playerData }) {
  const [bookings, setBookings] = useState([]);
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [walletData, setWalletData] = useState();
  const [transactionOpen, setTransactionOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState("Bookings")

  const handleSelectedOption = (value) => {
    setSelectedOption(value)
    value === "Transactions" ? setTransactionOpen(true) : setTransactionOpen(false);
  }
  // Handle filter change
  function handleFilterChange(filterParam) {
    const updatedFilters = appliedFilters.includes(filterParam)
      ? appliedFilters.filter((filter) => filter !== filterParam)
      : [...appliedFilters, filterParam];
    setAppliedFilters(updatedFilters);
  }
  // useEffect(() => {
  //   async function getUserToken() {
  //     let requestOptions = {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //       redirect: "follow",
  //     };
  //     const response = await fetch("/api/profile", requestOptions);
  //     const result = await response.json();
  //     if (result?.user?.token) {
  //       fetchData(result.user.token);
  //     }
  //   }
  //   const fetchData = async (token) => {
  //     try {
  //       const filterParams = appliedFilters.map((param) => `&${param}`).join("");
  //       const requestOptions = {
  //         method: "GET",
  //         headers: {
  //           "Content-Type": "application/json",
  //           Authorization: `Bearer ${token}`,
  //         },
  //         redirect: "follow"
  //       };
  //       const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/?playerId=${playerData._id}${filterParams}`, requestOptions);
  //       const result = await response.json();
  //       setBookings(result);
  //     } catch (error) {
  //       console.error("Error fetching bookings:", error);
  //     }
  //   };

  //   if (playerData?._id) {
  //     getUserToken();
  //   }
  // }, [playerData, appliedFilters]);

  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        let requestOptions = {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          redirect: "follow",
        };
        const response = await fetch("/api/profile", requestOptions);
        const result = await response.json();
        if (result?.user?.token) {
          const requestOptions = {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${result?.user?.token}`,
            },
            redirect: "follow"
          };
          const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/wallet?email=${playerData?.email}`, requestOptions);
          const result1 = await response.json();
          setWalletData(result1.data[0]);
        }

      } catch (error) {
        console.error("Error fetching wallet data:", error);
      }
    };

    if (playerData) {
      fetchWalletData();
    }
  }, [playerData]);

  return (
    <div className="md:w-full mt-4 md:mt-0 py-4 px-4 shadow-md rounded-md bg-white">
      <div className="flex justify-between items-center">
        <div className="flex gap-6 items-center text-lg">
          <div className="flex gap-2 items-baseline">
            <input
              type="radio"
              value="Bookings"
              checked={selectedOption === 'Bookings'}
              onChange={(value) => handleSelectedOption(value.target.value)}
            />
            <p>Bookings</p>
          </div>
          <div className="flex gap-2 items-baseline">
            <input
              type="radio"
              value="Transactions"
              checked={selectedOption === 'Transactions'}
              onChange={(value) => handleSelectedOption(value.target.value)}
            />
            <p>Transactions</p>
          </div>
        </div>
        {walletData && (
          <div className="flex items-center gap-2">
            <Image src="/Wallet.png" width={50} height={50} alt="Wallet" className="w-7" />
            <p>₹{walletData?.balance?.toFixed(2)}</p>
          </div>
        )}
      </div>
      {!transactionOpen && (<div className="flex items-center gap-2 md:gap-10 mt-6 mb-6 flex-wrap	">
        <FilterButton onClick={() => handleFilterChange("courseType=course")} active={appliedFilters.includes("courseType=course")}>Course</FilterButton>
        <FilterButton onClick={() => handleFilterChange("courseType=class")} active={appliedFilters.includes("courseType=class")}>Session</FilterButton>
        <FilterButton onClick={() => handleFilterChange("status=Active")} active={appliedFilters.includes("status=Active")}>Active</FilterButton>
        {/* <FilterButton onClick={() => handleFilterChange("status=In Progress")} active={appliedFilters.includes("status=In Progress")}>Active</FilterButton>
        <FilterButton onClick={() => handleFilterChange("status=Canceled")} active={appliedFilters.includes("status=Canceled")}>Cancelled</FilterButton> */}
        <FilterButton onClick={() => handleFilterChange("status=Inactive")} active={appliedFilters.includes("status=Inactive")}>Inactive</FilterButton>
      </div>)}
      <ol className="flex flex-col gap-6 divide-y divide-gray-100 text-sm leading-6 lg:col-span-7 xl:col-span-8 overflow-y-auto">
        {transactionOpen ?
          <Transactions playerData={playerData} />
          : <Booking bookings={bookings} playerData={playerData} appliedFilters={appliedFilters}/>}
      </ol>
    </div>
  );
}

