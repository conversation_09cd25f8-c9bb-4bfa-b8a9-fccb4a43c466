import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const resp = NextResponse.json({
      message: "Logout Success",
      success: true,
    });
    resp.cookies.set("token", "");
    resp.cookies.set("player_id", "");
    return resp;
  } catch (error) {
    console.error("error", error?.response?.data?.message);
    return new NextResponse(JSON.stringify({ error: error?.response?.data }));
  }
}
