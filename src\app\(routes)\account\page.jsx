"use client";
import { useState, useEffect } from "react";
import LeftDetails from "@/components/Account/LeftDetails/page";
import RightDetails from "@/components/Account/RightDetails/page";
import axios from "axios";
import { useRouter } from "next/navigation";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { AlertModal } from "@/components/AlertModal";
import { Country, State, City } from 'country-state-city';

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

export default function Account() {
  const router = useRouter();
  const [playerData, setPlayerData] = useState("");
  const [editSection, setEditSection] = useState(null);
  const [Sports, setSports] = useState([]);
  const [message, setMessage] = useState("");
  const [playerInfo, setPlayerInfo] = useState({
    token: "",
    id: "",
  });
  const [showModal, setShowModal] = useState(false);
  const [states, setStates] = useState([]);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  useEffect(() => {
    async function GetPlayerLogin() {
      try {
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        let requestOptions = {
          method: "POST",
          headers: myHeaders,
          redirect: "follow",
        };
        const response = await fetch("/api/profile", requestOptions);
        const result = await response.json();

        if (result.user) {
          const { token, player_id } = result.user;
          setPlayerInfo({ token, id: player_id });
          getPlayerData(token, player_id);
        } else {
          router.push("/login");
        }
      } catch (error) {
        console.log("error 38");
      }
    }

    async function getPlayerData(token, player_id) {
      try {
        const myHeaders = new Headers();
        myHeaders.append("Authorization", `Bearer ${token}`);

        const requestOptions = {
          method: "GET",
          headers: myHeaders,
          redirect: "follow",
        };
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${player_id}`,
          requestOptions
        );
        const result = await response.json();
        if (result) setPlayerData(result);
        if (!result.mobile) {
          setShowModal(!showModal);
          setMessage("Please Add mobile number to continue");
        }
        if (!result.homeState) {
          setShowModal(!showModal);
          setMessage("Please Update home state to continue");
        }
        if (!result.mobile && !result.mobile) {
          setShowModal(!showModal);
          setMessage("Please Add mobile number and home state to continue");
        }
      } catch (error) {
        console.log("error 59");
      }
    }

    GetPlayerLogin();

    async function fetchData() {
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/category`
        );
        setSports(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
    fetchData();
  }, []);

  const handleCloseModal = () => {
    setEditSection(null);

    document.body.classList.remove("overflow-hidden");
    
  };

  const handleCloseModalWithoutSaving = () =>{
    setEditSection(null);
    let updatedPlayerData = { ...playerData };
    if (!updatedPlayerData.hobbies) {
      updatedPlayerData.hobbies = [];
    }
    setPlayerData(updatedPlayerData);
    document.body.classList.remove("overflow-hidden");
  }

  const handleEdit = (section) => {
    setEditSection(section);
    document.body.classList.add("overflow-hidden");
  };

  const handleAddSport = () => {
    setPlayerData((prevData) => ({
      ...prevData,
      hobbies: [...(prevData.hobbies || []), { id: "" }],
    }));
  };

  const handleSaveChangesPersonal = async (values) => {
    try {
      const { token, id: player_id } = playerInfo;
      if (token && player_id) {
        // Update playerData with form values from Formik
        const updatedPlayerData = {
          ...playerData,
          firstName: values.firstName,
          lastName: values.lastName,
          mobile: values.mobile,
          email: values.email,
          schoolName: values.schoolName,
          homeState: values.homeState, // Get homeState from Formik values
        };

        let updateHeaders = new Headers();
        updateHeaders.append("Content-Type", "application/json");
        updateHeaders.append("Authorization", `Bearer ${token}`);

        let updateResponse = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${player_id}`,
          {
            method: "PATCH",
            headers: updateHeaders,
            body: JSON.stringify(updatedPlayerData),
          }
        );
        if (updateResponse.status == 300) {
          setMessage("Mobile number already exist with different account");
          setShowModal(!showModal);
        }
        if (updateResponse.ok) {
          setPlayerData(updatedPlayerData);
          handleCloseModal();
          window.location.reload();
        } else {
          console.error("Error updating player data");
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const handleSaveChangesSports = async () => {
    try {
      const { token, id: player_id } = playerInfo;
      if (token && player_id) {
        let updateHeaders = new Headers();
        updateHeaders.append("Content-Type", "application/json");
        updateHeaders.append("Authorization", `Bearer ${token}`);

        let updateResponse = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${player_id}`,
          {
            method: "PATCH",
            headers: updateHeaders,
            body: JSON.stringify({ hobbies: playerData.hobbies }),
          }
        );
        if (updateResponse.ok) {
          handleCloseModal();
          window.location.reload();
        } else {
          console.error("Error updating sports");
        }
      }
    } catch (error) {
      console.log("error 161");
    }
  };

  const handleSportSelection = (selectedSportId, index) => {
    let updatedPlayerData = { ...playerData };
    if (!updatedPlayerData.hobbies) {
      updatedPlayerData.hobbies = [];
    }
    updatedPlayerData.hobbies[index] = { id: selectedSportId };
    console.log("---", updatedPlayerData.hobbies)
    setPlayerData(updatedPlayerData);
  };

  const handleDeleteHobby = (index) => {
    console.log("---index", index);
    console.log("---->>>prevdata", playerData.hobbies);
  
    setPlayerData((prevData) => {
      const updatedHobbies = prevData.hobbies.filter((hobby, i) => {
        console.log("---hobby:", hobby, "i:", i, "index:", index); // Log the values
        return i !== index;
      });
      return { ...prevData, hobbies: updatedHobbies };
    });
  };
  
  // const handleDeleteHobby = (index) => {
  //   console.log("---index", index)
  //   console.log("---->>>prevdata", playerData.hobbies)
  //   setPlayerData((prevData) => ({...prevData,hobbies: prevData.hobbies.filter((hobby, i) => i !== index)}));
    
  // };
  console.log("---playerData", playerData.hobbies)
  const phoneRegExp =
    /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;

  const personalInfoSchema = Yup.object().shape({
    firstName: Yup.string()
      .trim()
      .min(3, "Please Type atleast 3 Letters")
      .max(15, "Cannot exceed 15 Letters")
      .required("Please enter your first name"),
    lastName: Yup.string()
      .trim()
      .min(3, "Please Type atleast 3 Letters")
      .max(15, "Cannot Exceed 15 Letters")
      .required("Please enter your last name"),
    mobile: Yup.string()
      .min(10, "Min Limit is 10 Digits")
      .max(10, "Max Limit is 10 Digits")
      .matches(phoneRegExp, "Phone number is not valid")
      .required("Please enter your number"),
    schoolName: Yup.string()
      .min(4, "School Must be atleast 4 Letters")
      .max(30, "Cannot Exceed 30 Letters"),
    homeState: Yup.string()
      .max(100, "Only 100 characters are allowed"),
  });

  return (
    <>
      <AlertModal
        show={showModal}
        onClose={() => setShowModal(!showModal)}
        message={message}
      />
      <div className="bg-gray-100">
        <div className="md:flex m-auto gap-4 max-w-7xl md:flex-col px-4 pb-4 md:p-6">
          <LeftDetails playerData={playerData} handleEdit={handleEdit} />
          <RightDetails playerData={playerData} />
        </div>
      </div>
      {editSection === "personal" && (
        <div className="fixed z-10 inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 w-full">
          <div className="relative min-w-[95%] md:min-w-[53%]  bg-white px-4 md:px-8 py-4 rounded-md min-h-[17rem]">
            <svg
              onClick={handleCloseModal}
              className="cursor-pointer absolute top-4 right-4 w-[18px]"
              aria-label="Close panel"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 12.346 12.346"
            >
              <g
                id="Group_650"
                data-name="Group 650"
                transform="translate(-63.209 -2237.327)"
              >
                <line
                  id="Line_249"
                  data-name="Line 249"
                  x1="11.639"
                  y2="11.639"
                  transform="translate(63.563 2237.681)"
                  fill="none"
                  stroke="#231f20"
                  strokeWidth="1"
                ></line>
                <line
                  id="Line_250"
                  data-name="Line 250"
                  x2="11.639"
                  y2="11.639"
                  transform="translate(63.563 2237.681)"
                  fill="none"
                  stroke="#231f20"
                  strokeWidth="1"
                ></line>
              </g>
            </svg>

            <h3 className="md:text-xl text-lg text-center font-semibold mb-12">
              Edit Personal Information
            </h3>
            <Formik
              initialValues={{
                firstName: playerData.firstName || "",
                lastName: playerData.lastName || "",
                mobile: playerData.mobile || "",
                email: playerData.email || "",
                schoolName: playerData.schoolName || "",
                homeState: playerData.homeState || "",
              }}
              validationSchema={personalInfoSchema}
              onSubmit={handleSaveChangesPersonal}
            >
              {({ isSubmitting, isValid }) => (
                <Form>
                  <div className="flex flex-col md:flex-row justify-around w-full gap-8 mb-4">
                    <div className="flex w-full md:w-1/2 items-baseline">
                      <label className="w-[35%] md:w-[7rem]">
                        First Name :
                      </label>
                      <div className="flex flex-col">
                        <Field
                          type="text"
                          name="firstName"
                          className="border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem]"
                        />
                        <ErrorMessage
                          name="firstName"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    </div>
                    <div className="flex w-full md:w-1/2 items-baseline">
                      <label className="w-[35%] md:w-[7rem]">Last Name :</label>
                      <div className="flex flex-col">
                        <Field
                          type="text"
                          name="lastName"
                          className="border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem]"
                        />
                        <ErrorMessage
                          name="lastName"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row justify-between w-full gap-8 mb-4">
                    <div className="flex w-full md:w-1/2 items-baseline">
                      <label className="w-[35%] md:w-[7rem]">Phone No. :</label>
                      <div className="flex flex-col">
                        <Field
                          type="number"
                          name="mobile"
                          style={{
                            WebkitAppearance: "none",
                            MozAppearance: "textfield",
                          }}
                          className="border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem]"
                        />
                        <ErrorMessage
                          name="mobile"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    </div>
                    <div className="flex w-full md:w-1/2 items-center">
                      <label className="w-[35%] md:w-[7rem]">Email :</label>
                      <input
                        disabled
                        type="email"
                        name="email"
                        defaultValue={playerData.email}
                        className="border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem]"
                      />
                    </div>
                  </div>

                  <div className="flex w-full items-center mb-2">
                    <div className="flex w-full md:w-1/2 items-baseline">
                      <label className="w-[35%] md:w-[7rem]">Home State:</label>
                      <div className="flex flex-col">
                        <Field
                          as="select"
                          name="homeState"
                          style={{
                            WebkitAppearance: "none",
                            MozAppearance: "textfield",
                          }}
                          
                          className={`border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem] 
                            }`}
                        >
                          <option value="">Select State</option>
                          {states.map((state) => (
                            <option key={state.isoCode} value={state.isoCode}>
                              {state.name}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage
                          name="homeState"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    </div>
                  </div>


                  <div className="flex w-full items-center mb-2">
                    <div className="flex w-full md:w-1/2 items-baseline">
                      <label className="w-[35%] md:w-[7rem]">
                        School Name :
                      </label>
                      <div className="flex flex-col">
                        <Field
                          type="text"
                          name="schoolName"
                          style={{
                            WebkitAppearance: "none",
                            MozAppearance: "textfield",
                          }}
                          className="border border-gray-300 rounded-md p-1 w-[65%] md:w-[15rem]"
                        />
                        <ErrorMessage
                          name="schoolName"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end mt-8 md:mt-0">
                    <button
                      onClick={handleSaveChangesPersonal}
                      disabled={isSubmitting || !isValid}
                      className={`${isSubmitting || !isValid ? "bg-gray-500" : "bg-black"
                        } text-white px-4 py-2 rounded-md`}
                    >
                      Save Changes
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      )}
      {editSection === "sports" && (
        <div className="fixed z-10 w-full inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50">
          <div className="relative w-[90%] md:w-[30%] flex flex-col justify-center item-center text-center bg-white px-8 py-4 rounded-md min-h-[10rem]">
            <svg
              onClick={handleCloseModal}
              className="cursor-pointer absolute top-4 right-4 w-[18px]"
              aria-label="Close panel"
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 12.346 12.346"
            >
              <g
                id="Group_650"
                data-name="Group 650"
                transform="translate(-63.209 -2237.327)"
              >
                <line
                  id="Line_249"
                  data-name="Line 249"
                  x1="11.639"
                  y2="11.639"
                  transform="translate(63.563 2237.681)"
                  fill="none"
                  stroke="#231f20"
                  strokeWidth="1"
                ></line>
                <line
                  id="Line_250"
                  data-name="Line 250"
                  x2="11.639"
                  y2="11.639"
                  transform="translate(63.563 2237.681)"
                  fill="none"
                  stroke="#231f20"
                  strokeWidth="1"
                ></line>
              </g>
            </svg>
            <h3 className="text-lg md:text-xl font-semibold mb-12">
              Edit Sports Information
            </h3>
            <div className="max-h-[260px] overflow-y-auto">
              {playerData?.hobbies?.map((hobby, index) => {
                return (
                  <div
                    key={index}
                    className="flex items-center justify-center gap-6 mb-8"
                  >
                    <label>Sport {index + 1} : </label>
                    <select
                      name={`hobby_${index}`}
                      value={hobby?.id._id}
                      onChange={(e) => {
                        console.log("---hobbyid", hobby)
                        handleSportSelection(e.target.value, index);
                      }}
                      className="border border-gray-300 rounded-md p-1"
                    >
                      <option value="">Select a sport</option>
                      {Sports?.data &&
                        Sports.data.map((sport, ind) => { 
                          // console.log("---->>sport", sport, ind)
                          const isSportSelected = playerData?.hobbies?.some(
                            (s) =>
                              s?.id?._id === sport?._id || s?.id === sport?._id
                          );
                          // console.log("Is Sport Disabled?", sport?.name, sport?._id, isSportSelected);
                          return (
                            <option
                              key={sport._id}
                              value={sport._id}
                              disabled={isSportSelected}
                            >
                              {sport?.name}
                            </option>
                          );
                        })}
                    </select>

                    <button onClick={() => handleDeleteHobby(index)}>
                      {/* delete */}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                        fill="transparent"
                        stroke="currentColor"
                        className="w-5 h-5 text-black"
                      >
                        <title>Remove</title>
                        <path
                          d="M4 6H16"
                          strokeWidth="1.25"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                        <path
                          d="M8.5 9V14"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                        <path
                          d="M11.5 9V14"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                        <path
                          d="M5.5 6L6 17H14L14.5 6"
                          strokeWidth="1.25"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        ></path>
                        <path
                          d="M8 6L8 5C8 4 8.75 3 10 3C11.25 3 12 4 12 5V6"
                          strokeWidth="1.25"
                        ></path>
                      </svg>
                    </button>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-center flex-col items-center gap-6">
              <button
                onClick={handleAddSport}
                className="flex items-center justify-center bg-white w-[90%]text-xl border border-solid px-4 py-2 rounded-md text-stone-300"
              >
                <svg
                  className="cursor-pointer w-[13px] rotate-45"
                  aria-label="Close panel"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 12.346 12.346"
                >
                  <g
                    id="Group_650"
                    data-name="Group 650"
                    transform="translate(-63.209 -2237.327)"
                  >
                    <line
                      id="Line_249"
                      data-name="Line 249"
                      x1="11.639"
                      y2="11.639"
                      transform="translate(63.563 2237.681)"
                      fill="none"
                      stroke="#A9A9A9"
                      strokeWidth="1"
                    ></line>
                    <line
                      id="Line_250"
                      data-name="Line 250"
                      x2="11.639"
                      y2="11.639"
                      transform="translate(63.563 2237.681)"
                      fill="none"
                      stroke="#A9A9A9"
                      strokeWidth="1"
                    ></line>
                  </g>
                </svg>
              </button>
              <button
                onClick={handleSaveChangesSports}
                className="bg-black text-white px-4 py-2 rounded-md"
              >
                Save Changes
              </button>
              {/* <button
                onClick={handleCloseModal}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md"
              >
                Cancel
              </button> */}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
