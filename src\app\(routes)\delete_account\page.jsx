"use client";

import { AlertModal } from "@/components/AlertModal";
import React, { useState } from "react";
import Link from "next/link";
import { signOut } from "next-auth/react";


const ConfirmAccountDeletion = () => {
  const [reason, setReason] = useState("");
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");

  const [loading, setLoading] = useState(false);

  const handleCloseAlertModal = () => {
    setShowAlertModal(false);
    setLoading(false);
  };

  const handleLogout = () => {
    if (localStorage.getItem("Booked")) localStorage.removeItem("Booked");
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch(`/api/logout`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result) {
          signOut({ callbackUrl: "/login" });
        }
      })
      .catch((error) => {
        console.log("error 34");
      });
  };

  const handleDeleteAccount = () => {
    setLoading(true);
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };

    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.user) {
          const myHeaders = new Headers();
          myHeaders.append("Authorization", `Bearer ${result.user.token}`);

          const requestOptions = {
            method: "DELETE",
            headers: myHeaders,
            redirect: "follow",
          };

          fetch(`/api/delete/`, requestOptions)
            .then((response) =>
              response
                .json()
                .then((data) => ({ status: response.status, data }))
            )
            .then(({ status, data }) => {
              if (status === 200) {
                setLoading(false);
                setAlertMessage(data.message);
                setShowAlertModal(true);
                signOut({ callbackUrl: "/login" });
                handleLogout();
              } else {
                setLoading(false);

                setAlertMessage(data.error || "Failed to delete account");
                setShowAlertModal(true);
              }
            })
            .catch((error) => {
              console.error(error);
              setLoading(false);
              setAlertMessage("An unexpected error occurred");
              setShowAlertModal(true);
            });
        }
      })
      .catch((error) => {
        console.log(error);
        setAlertMessage("An unexpected error occurred");
        setShowAlertModal(true);
      });
  };

  const handleConfirm = () => {
    if (reason) {
      // Add your account deletion logic here
      handleDeleteAccount();
    } else {
      alert("Please enter your reason");
    }
  };

  return (
    <>
      <AlertModal
        show={showAlertModal}
        onClose={handleCloseAlertModal}
        message={alertMessage}
      />

      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
          <h2 className="text-xl font-semibold text-center mb-4">
            Confirm Account Deletion
          </h2>
          <p className="text-gray-800 mb-4">
            Are you sure? Your profile and related account information will be
            deleted from our site.
          </p>

          <p className="text-black-600 font-bold mb-4">
            Note:- Players who has booked sessions can not delete there account.
          </p>
          <p className="text-yellow-600 font-bold mb-4">
            To confirm deletion, please enter your reason below:
          </p>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-lg mb-4"
            placeholder="Please enter your reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
          />
          <div className="flex justify-end space-x-4">
            <button
              className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600"
              onClick={handleConfirm}
              disabled={loading}
            >
              {loading ? "Loading..." : "Confirm deletion"}
            </button>
            <Link
              href="/account"
              className="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default ConfirmAccountDeletion;
