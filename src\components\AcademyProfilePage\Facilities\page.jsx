import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function Facilities({ data, blockData }) {
  const [homeAcademies, setHomeAcademies] = useState([]);

  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeAcademies(sortedData);
  }, [data]);

  return (
    <div>
      {homeAcademies.length > 0 && (
        <div className="bg-white">
          <div className="mx-auto max-w-2xl px-4 md:py-10 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
            <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-6 uppercase">
              {blockData?.title || 'FACILITIES'}
            </h2>

            <Carousel
              additionalTransfrom={0}
              arrows
              autoPlaySpeed={3000}
              centerMode={false}
              containerClass="container desktopView"
              draggable
              focusOnSelect={false}
              infinite={true}
              itemClass="carousel-item-padding-40-px"
              keyBoardControl
              minimumTouchDrag={80}
              renderButtonGroupOutside={true}
              renderDotsOutside={false}
              responsive={{
                desktop: {
                  breakpoint: {
                    max: 3000,
                    min: 1024,
                  },
                  items: 4,
                  partialVisibilityGutter: 40,
                },
                mobile: {
                  breakpoint: {
                    max: 464,
                    min: 0,
                  },
                  items: 1.5,
                  infinite: false,
                  partialVisibilityGutter: 40,
                },
                tablet: {
                  breakpoint: {
                    max: 1024,
                    min: 464,
                    infinite: false,
                  },
                  items: 3,
                  partialVisibilityGutter: 40,
                },
              }}
              showDots={false}
              sliderClass=""
              slidesToSlide={1}
              swipeable
            >
              {homeAcademies &&
                homeAcademies.map((facilityItem, index) => {
                  const details = facilityItem?.facilityDetails;
                  if (!details) return null;
                  const address = [
                    details.addressLine1,
                    details.addressLine2,
                    details.city,
                    details.state
                  ].filter(Boolean).join(", ");
                  return (
                    <div key={index} className="flex flex-col items-start gap-2 p-4">
                      <div className="flex items-center gap-3">
                        <Image
                          src="/location.png"
                          alt="Location Icon"
                          width={35}
                          height={35}
                        />
                        <div className="flex flex-col">
                          <span style={{ color: 'rgba(43, 43, 42, 0.6)', fontSize: '14px' }}>
                          {details.name} {address}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </Carousel>
          </div>
        </div>
      )}
    </div>
  );
} 