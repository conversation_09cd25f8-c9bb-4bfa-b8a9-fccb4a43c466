import { useState, useEffect } from "react";
import { Dialog, Disclosure } from "@headlessui/react";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Image from "next/image";
import Link from "next/link";
import { signOut } from "next-auth/react";
import QRScanner from "../QRScanner/page";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}
const handleLogout = () => {
  if (localStorage.getItem("Booked")) localStorage.removeItem("Booked");
  var myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");
  var requestOptions = {
    method: "POST",
    headers: myHeaders,
    redirect: "follow",
  };
  fetch(`/api/logout`, requestOptions)
    .then((response) => response.json())
    .then((result) => {
      if (result) {
        signOut({ callbackUrl: "/login" });
      }
    })
    .catch((error) => {
      console.log("error 30");
    });
};

export default function MobileNavBar({ Sports, userExist, navigation, pathname }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [playerObject, setPlayerObject] = useState({});
  const [showScanner, setShowScanner] = useState(false);
  useEffect(() => {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch("/api/profile", requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.user) {
          const myHeaders = new Headers();
          myHeaders.append("Authorization", `Bearer ${result.user.token}`);

          const requestOptions = {
            method: "GET",
            headers: myHeaders,
            redirect: "follow",
          };

          fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${result.user.player_id}`,
            requestOptions
          )
            .then((response) => response.json())
            .then((result) => setPlayerObject(result))
            .catch((error) => console.error(error));
        }
      })
      .catch((error) => {
        console.log("error 69");
      });
  }, [pathname]);

  return (
    <header className="bg-gray-100 flex items-center gap-2">
      {userExist && (
        <div className="flex sm:hidden z-10">
          <button onClick={() => setShowScanner(!showScanner)}>
            {showScanner ? <Image src="/close.png" width={20} height={20} alt="close icon" className="w-5"/> : <Image src="/Attendance.svg" width={20} height={20} alt="Attendance Icon" className="w-6"/>}
          </button>
          {showScanner && (<QRScanner playerObject={playerObject} setShowScanner={setShowScanner}/>)}
        </div>
      )}
      <nav
        className="mx-auto flex sm:hidden max-w-7xl items-center justify-between sm:p-6 lg:px-8"
        aria-label="Global"
      >
        <div className="flex lg:hidden">
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md  text-gray-700"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-8 w-8" aria-hidden="true" />
          </button>
        </div>
      </nav>
      <Dialog
        as="div"
        className="lg:hidden"
        open={mobileMenuOpen}
        onClose={setMobileMenuOpen}
      >
        <div className="fixed inset-0 z-10" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-[9999] w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <Link href="/" className="">
              <Image
                className="h-8 w-auto"
                width={100}
                height={100}
                src="/MainLogo.svg"
                alt="MainLogo.svg"
                style={{ width: "104px", height: "auto" }}
              />
            </Link>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
                <Disclosure as="div" className="-mx-3">
                  {({ open }) => (
                    <>
                      <Disclosure.Button className="flex w-full items-center justify-between rounded-lg py-2 pl-3 pr-3.5 text-base font-semibold leading-7 text-black hover:bg-gray-50">
                        Sports
                        <ChevronDownIcon
                          className={classNames(
                            open ? "rotate-180" : "",
                            "h-5 w-5 flex-none"
                          )}
                          aria-hidden="true"
                        />
                      </Disclosure.Button>
                      <Disclosure.Panel className="mt-2 space-y-2">
                        {Sports && Sports?.map((item) => (
                          <Disclosure.Button
                            key={item.name}
                            as="a"
                            href={`/categories/${item.handle.toLowerCase()}?category=${item.name.toLowerCase()}`}
                            className="block rounded-lg py-2 pl-6 pr-3 text-sm font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                          >
                            {item.name}
                          </Disclosure.Button>
                        ))}
                      </Disclosure.Panel>
                    </>
                  )}
                </Disclosure>
                {navigation.map((item) => (
                  <a
                  
                    key={item.name}
                    href={item.href}
                    className="block rounded-lg py-2 text-md font-semibold leading-7 text-black hover:bg-gray-50"
                  >
                    {item.name}
                  </a>
                ))}
                {userExist ? (
                  <Disclosure as="div" className="-mx-3">
                    {({ open }) => (
                      <>
                        <Disclosure.Button className="flex w-full items-center justify-between rounded-lg py-2 pl-3 pr-3.5 text-base font-semibold leading-7 text-black hover:bg-gray-50">
                          {playerObject.firstName} {playerObject.lastName}
                          <ChevronDownIcon
                            className={classNames(
                              open ? "rotate-180" : "",
                              "h-5 w-5 flex-none"
                            )}
                            aria-hidden="true"
                          />
                        </Disclosure.Button>
                        <Disclosure.Panel className="mt-2 space-y-2">
                          <a
                            href="/account"
                            className="block rounded-lg py-2 pl-6 pr-3 text-sm font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                          >
                            Account
                          </a>
                          <div
                            onClick={handleLogout}
                            className="block rounded-lg py-2 pl-6 pr-3 text-sm font-semibold leading-7 text-gray-700 hover:bg-gray-50"
                          >
                            Logout
                          </div>
                        </Disclosure.Panel>
                      </>
                    )}
                  </Disclosure>
                ) : (
                  <a
                    href="/login"
                    className="flex w-full items-center justify-between rounded-lg py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                  >
                    Sign In
                  </a>
                )}

              </div>
            </div>
          </div>
        </Dialog.Panel>
      </Dialog>
    </header>
  );
}