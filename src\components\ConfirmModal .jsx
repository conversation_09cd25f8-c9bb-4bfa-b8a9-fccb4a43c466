export const ConfirmModal = ({ show, onClose, onConfirm }) => {
    if (!show) return null;
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="bg-white p-6 rounded-lg shadow-lg z-10">
          <h2 className="text-xl font-semibold mb-4">Confirm Delete</h2>
          <p className="mb-4">Are you sure you want to delete your account?</p>
          <div className="flex justify-end">
            <button
              className="px-4 py-2 mr-2 bg-gray-300 rounded"
              onClick={onClose}
            >
              No
            </button>
            <button
              className="px-4 py-2 bg-red-500 text-white rounded"
              onClick={onConfirm}
            >
              Yes
            </button>
          </div>
        </div>
      </div>
    );
  };
  