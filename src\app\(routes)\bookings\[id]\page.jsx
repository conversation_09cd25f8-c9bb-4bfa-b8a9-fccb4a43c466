"use client";
import BookingLeft from "@/components/Bookings/LeftPage.jsx/page";
import BookingRight from "@/components/Bookings/RightPage.jsx/page";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";

export default function Bookings() {
  const [bookingData, setBookingData] = useState();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      getUserToken();
    }
  }, [id]);

  async function getUserToken() {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result = await response.json();
    if (result?.user?.token) {
      getBookingById(result.user.token);
    }
  }

  async function getBookingById(token) {
    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${id}`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        setBookingData(result);
      })
      .catch((error) => console.error(error));
  }

  return (
    <div className="md:flex md:flex-col m-auto gap-16 max-w-7xl p-4 mt-5 mb-5">
      <h1 className="md:text-[2rem] items-center mx-auto">Booking Details</h1>
      <div className="flex flex-col md:flex-row gap-8">
        <BookingLeft bookingData={bookingData} />
        <BookingRight bookingData={bookingData} />
      </div>
    </div>
  );
}

