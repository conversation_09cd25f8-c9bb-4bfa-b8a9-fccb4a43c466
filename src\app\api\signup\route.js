import axios from "axios";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/player`,
      reqBody,
      { headers: { "Content-Type": "application/json" } }
    );
    const token = response?.data?.token;
    const id = response?.data?.data?._id;
    const resp = NextResponse.json({
      message: "SignUp Success",
      success: true,
    });
    resp.cookies.set("token", token, { httpOnly: true, secure: true });
    resp.cookies.set("player_id", id, { httpOnly: true, secure: true });
    return resp;
  } catch (error) {
    console.log("Server Error 22")
    return new NextResponse(JSON.stringify({ error: error?.response?.data?.error }));
  }
}
