import axios from "axios";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    
    const { email } = reqBody;  // Extract the email from the request body
    
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/requestResetPassword/${email}`,
      { headers: { "Content-Type": "application/json" } }
    ); 
    const token = response?.data?.token;
    const id = response?.data?.id;
    
    const resp = NextResponse.json({
      message: "Link sent successfully on mail",
      success: true,
    });
    
    // Set cookies if necessary
    // resp.cookies.set("token", token, { httpOnly: true, secure: true });
    // resp.cookies.set("player_id", id, { httpOnly: true, secure: true });
    
    return resp;
  } catch (error) {
    console.error("error",(error.response.data.error));
    return new NextResponse(JSON.stringify({ error: error.response.data.error}), { status: error.response?.status || 500 });
  }
}
