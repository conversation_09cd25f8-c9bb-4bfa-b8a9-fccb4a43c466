import React from "react";

function PriceBreakdownModal({
  setShowPriceSummary,
  handleRazorPay,
  bookingArray,
  data,
}) {
  let total = 0;
  for (const booking of bookingArray) {
    const price = Number(booking.price.replace(/[^\d.]/g, ""));
    total += price;
  }
  const platformFee = data?.academy_id?.platformFee
    ? data.academy_id.platformFee / 100
    : 0.12;

  const platformTax = data.classType === "course"
    ? data.fees.feesCourse * platformFee
    : total * platformFee;

  // let platformTax = data.classType === "course" ? data.fees.feesCourse * 0.12 : total * 0.12;
  let gst = platformTax * 0.18;
  let coachGst = data?.coach_id?.hasGst
    ? data.classType === "course"
      ? data.fees.feesCourse * 0.18
      : total * 0.18
    : 0;
  let finalPrice = Math.ceil(
    (data.classType === "course" ? data.fees.feesCourse : total) + platformTax + gst + coachGst);
  const handleCloseModal = () => {
    setShowPriceSummary(false);
    document.body.classList.remove("overflow-hidden");
  };
  const handleConfirmModal = () => {
    handleRazorPay();
    setShowPriceSummary(false);
    document.body.classList.remove("overflow-hidden");
  };
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-[90%]">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Price Breakdown
                </h3>
                <div className="mt-2">
                  <div className="mb-2 flex items-center justify-between">
                    <p className="text-gray-700">Base Price: </p>
                    <p className="text-gray-700">
                      ₹{" "}
                      {data.classType === "course"
                        ? data.fees.feesCourse
                        : total}
                    </p>
                  </div>
                  <div className="mb-2 flex items-center justify-between">
                    <p className="text-gray-700">Platform Fees:</p>
                    <p className="text-gray-700">₹ {platformTax.toFixed(2)}</p>
                  </div>
                  <div className="mb-2  flex items-center justify-between">
                    <p className="text-gray-700">GST(18%):</p>
                    <p className="text-gray-700">₹ {gst.toFixed(2)}</p>
                  </div>
                  {data.coach_id?.hasGst && (
                    <div className="mb-2  flex items-center justify-between">
                      <p className="text-gray-700">Coach GST(18%):</p>
                      <p className="text-gray-700">₹ {coachGst.toFixed(2)}</p>
                    </div>
                  )}
                  <div className="mb-2  flex items-center justify-between">
                    <p className="text-gray-700">Total Amount:</p>
                    <p className="text-gray-700 font-semibold">
                      ₹ {finalPrice}{" "}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleConfirmModal}
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Confirm
            </button>
            <button
              onClick={handleCloseModal}
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PriceBreakdownModal;
