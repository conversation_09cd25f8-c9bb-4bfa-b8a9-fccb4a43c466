'use client'
import { useEffect, useState } from "react";

export default function RegisterAsCoach() {
  const [registerAsCoach, setRegisterAsCoach] = useState(null);

  useEffect(() => {
    const fetchTermsOfService = async () => {
      const requestOptions = {
        method: "GET",
        redirect: "follow"
      };
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/cms/cms-registration-details`, requestOptions);
        const result = await response.json();
        setRegisterAsCoach(result[0]?.registrationData);
      } catch (error) {
        console.error('Error fetching terms of service:', error);
      }
    };
    fetchTermsOfService();
  }, []);

  return (
    <div className="mx-auto max-w-7xl py-4 px-4 sm:py-6 sm:px-6 lg:py-8 lg:px-8">
      {registerAsCoach && <div dangerouslySetInnerHTML={{ __html: registerAsCoach }} />}
    </div>
  );
}
