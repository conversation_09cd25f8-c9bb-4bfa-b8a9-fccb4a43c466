import { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
export default function Transactions({ playerData }) {
  const [transactions, setTransactions] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(true);
  const [fetching, setFetching] = useState(false);
  useEffect(() => {
    if (playerData?._id) {
      fetchTransactions();
    }
  }, [playerData, pageNumber]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const requestOptions = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        redirect: "follow",
      };
      const response = await fetch("/api/profile", requestOptions);
      const result = await response.json();
      if (result?.user?.token) {
        const requestOptions = {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${result?.user?.token}`,
          },
          redirect: "follow",
        };
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/transactions?playerId=${playerData?._id}&page=${pageNumber}`,
          requestOptions
        );
        const result1 = await response.json();
        if (result1.data.length === 0) {
          setHasMore(false);
        } else {
          setTransactions((prevTransactions) => [
            ...prevTransactions,
            ...result1.data,
          ]);
        }
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
    } finally {
      setLoading(false);
      setFetching(false);
    }
  };

  const fetchMoreData = () => {
    setFetching(true);
    setLoading(true);
    setPageNumber((prevPageNumber) => prevPageNumber + 1);
  };
  const loader = (
    <div
      role="status"
      className="flex justify-center items-center overflow-y-auto mt-7"
    >
      <svg
        aria-hidden="true"
        className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-blue-500"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg "
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  );
  const formatDate = (isoString) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based in JavaScript
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {loading && pageNumber === 1 ? (
        <div className="text-center">
          <div role="status" className="flex justify-center items-center">
            <svg
              aria-hidden="true"
              className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-blue-500"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      ) : (
        <InfiniteScroll
          dataLength={transactions.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={loader} // Loader for subsequent fetches
          endMessage={
            transactions?.length > 0 && (
              <p>
                <br />
                No more transactions to show
              </p>
            )
          }
        >
          {transactions?.length > 0 ? (
            <div className="-mx-4 mt-8 flow-root sm:mx-0">
              <table className="min-w-full">
                <colgroup>
                  <col className="w-full sm:w-1/2" />
                  <col className="sm:w-1/6" />
                  <col className="sm:w-1/6" />
                  <col className="sm:w-1/6" />
                </colgroup>
                <thead className="border-b border-gray-300 text-black">
                  <tr>
                    <th
                      scope="col"
                      className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-black sm:pl-0"
                    >
                      Course/Session
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-right text-sm font-semibold text-black sm:table-cell"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3.5 text-right text-sm font-semibold text-black sm:table-cell"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-black sm:pr-0"
                    >
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((obj, index) => (
                    <tr key={index} className="border-b border-gray-200">
                      <td className="max-w-0 py-5 pl-4 pr-3 text-sm sm:pl-0">
                        <div className="font-medium text-black">
                          {obj?.courseId?.courseName}
                        </div>
                      </td>
                      <td className="px-3 py-5 text-right text-sm text-gray-500 sm:table-cell">
                        {formatDate(obj.date)}
                      </td>
                      <td className="px-3 py-5 text-right text-sm text-gray-500 sm:table-cell">
                        {obj?.type}
                      </td>
                      <td className="py-5 pl-3 pr-4 text-right text-sm text-gray-500 sm:pr-0">
                        {obj.amount}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="-mx-4 mt-8 flow-root sm:mx-0">
              <p>No Transactions available</p>
            </div>
          )}
        </InfiniteScroll>
      )}
    </div>
  );
}
