import axios from "axios";
import { NextResponse } from "next/server";
import { verifyToken } from "@/app/helpers/getDataFromToken";
export async function DELETE(req, res) {
    try {
        // Extract the token from cookies
        const token = await req.cookies.get("token")?.value || "";
        // Verify the token and extract player_id
        const payload = await verifyToken(token);
        const player_id = payload.id;
        // Make the DELETE request to the API endpoint
        const response = await axios.delete(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${player_id}`,
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );

        // Check if the response status is 200
        if (response.status === 200) {
            return NextResponse.json({
                message: "Account deleted successfully",
                success: true,
            });
        } else {
            throw new Error("Failed to delete account");
        }
    } catch (error) {
        console.error("Error deleting account:", error);
        return new NextResponse(
            JSON.stringify({ error: error.response?.data?.error || "Internal Server Error" }),
            { status: error.response?.status || 500 }
        );
    }
}
