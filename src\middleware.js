import { NextResponse } from "next/server";

export async function middleware(request) {
  const token = request.cookies.get("token")?.value || "";
  const id = request.cookies.get("player_id")?.value || "";

  let redirect = false;

  if (token && id) {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();

      if (!result.mobile) {
        redirect = true;
      }
    } catch (error) {
      console.log(error);
    }
  }

  if (!request.nextUrl.pathname.startsWith("/account") && redirect) {
    return NextResponse.redirect(new URL("/account", request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/",
    "/courses/:path*",
    "/coaches/:path*",
    "/account",
    "/categories/:path*",
    "/bookings/:path*",
  ],
};
