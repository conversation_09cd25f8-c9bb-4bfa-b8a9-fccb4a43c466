import axios from "axios";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
    let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(reqBody),
    };
 

    const response = axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/api/player/resetPassword/`,reqBody,{headers:{
        "Content-Type":"application/json",
    }})
    
    const token = response?.data?.token;
    const id = response?.data?.id;
    
    const resp = NextResponse.json({
      message: "Password Updates",
      success: true,
    });
    // Set cookies if necessary
    // resp.cookies.set("token", token, { httpOnly: true, secure: true });
    // resp.cookies.set("player_id", id, { httpOnly: true, secure: true });
    
    return resp;
  } catch (error) {
    console.error("error", JSON.stringify(error.response.data.error));
    return new NextResponse(JSON.stringify({ error: error.response.data.error}), { status: error.response?.status || 500 });
  }
}
