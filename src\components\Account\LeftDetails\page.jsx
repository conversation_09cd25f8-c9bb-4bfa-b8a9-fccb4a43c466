"use client";
import Image from "next/image";
import { useState } from "react";
import { Country, State, City } from 'country-state-city';

export default function LeftDetails({ playerData, handleEdit }) {
  const homeStateName = State.getStateByCodeAndCountry(playerData.homeState, "IN")?.name || "";
  return (
    <div className="md:w-full flex flex-col md:flex-row gap-4">
      <div className="md:w-1/2 p-4 shadow-md rounded-md flex flex-col gap-4 bg-white">
        <div className="flex justify-between mb-6">
          <h3 className="text-xl">Personal Information</h3>
          <button onClick={() => handleEdit("personal")}>
            <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
              <path d="M191.154-184h67.461l365.387-365.386-67.461-67.462-365.387 365.387V-184Zm597.152-408.77L599.309-782.537l51.154-51.153q26.615-26.615 61.845-26.115 35.23.5 60.845 27.115l66.692 67.691q25.615 25.615 25.23 60.73-.385 35.115-26 60.73l-50.769 50.769Zm-42.383 41.999-450.77 452.77H105.156V-288l451.769-451.77 188.998 188.998Zm-155.844-32.538-33.538-33.539 67.461 67.462-33.923-33.923Z"></path>
            </svg>
          </button>
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex justify-between flex-col md:flex-row gap-4">
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[6.5rem]">First Name :</h6>
              <p className="text-sm text-black">{playerData.firstName}</p>
            </div>
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[6.5rem]">Last Name :</h6>
              <p className="text-sm text-black">{playerData.lastName}</p>
            </div>
          </div>
          <div className="flex justify-between flex-col md:flex-row gap-4">
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[6.5rem]">Phone No. :</h6>
              <p className="text-sm text-black">{playerData.mobile}</p>
            </div>
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[3rem]">Email:</h6>
              <p className="text-sm text-black">{playerData.email}</p>
            </div>
          </div>
          <div className="flex justify-between flex-col md:flex-row gap-4">
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[6.5rem]">School Name :</h6>
              <p className="text-sm text-black line-clamp-2">
                {playerData.schoolName}
              </p>
            </div>
          </div>
          <div className="flex justify-between flex-col md:flex-row gap-4">
            <div className="text-base flex gap-2 items-center w-full md:w-1/2">
              <h6 className="text-gray-500 min-w-[6.5rem]">Home state :</h6>
              <p className="text-sm text-black line-clamp-2">
                {homeStateName}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="md:w-1/2 p-4 shadow-md rounded-md flex flex-col gap-4 bg-white">
        <div className="flex justify-between mb-6">
          <h3 className="text-xl">Sports you like most</h3>
          <button onClick={() => handleEdit("sports")}>
            <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
              <path d="M191.154-184h67.461l365.387-365.386-67.461-67.462-365.387 365.387V-184Zm597.152-408.77L599.309-782.537l51.154-51.153q26.615-26.615 61.845-26.115 35.23.5 60.845 27.115l66.692 67.691q25.615 25.615 25.23 60.73-.385 35.115-26 60.73l-50.769 50.769Zm-42.383 41.999-450.77 452.77H105.156V-288l451.769-451.77 188.998 188.998Zm-155.844-32.538-33.538-33.539 67.461 67.462-33.923-33.923Z"></path>
            </svg>
          </button>
        </div>

        {playerData?.hobbies?.length > 0 ? (
          <div className="flex gap-8 flex-wrap">
            {playerData?.hobbies?.map((hobby, index) => (
              hobby?.id?.image ? (
                <div key={index} className="flex flex-col items-center gap-2">
                  <Image
                    className="w-10 h-10 rounded-[100px] object-cover"
                    src={hobby?.id?.image}
                    alt="Account Hobbies"
                    width={50}
                    height={50}
                  />
                  <p>{hobby?.id?.name}</p>
                </div>
              ) : null
            ))}
          </div>
        ) : (
          <p>No sports added</p>
        )}

      </div>

    </div>
  );

}
