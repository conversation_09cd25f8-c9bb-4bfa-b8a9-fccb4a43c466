import Image from "next/image";

const blocksData = [
  {
    key: "coachingQualifications",
    title: "Coaching Qualification",
    imageSrc: "/CoachBlock1.png",
  },
  {
    key: "coachingExperience",
    title: "Coaching Experience",
    imageSrc: "/CoachBlock2.png",
  },
  {
    key: "playerExperience",
    title: "Playing Experience",
    imageSrc: "/CoachBlock1.png",
  },
  {
    key: "award",
    title: "Awards",
    imageSrc: "/CoachBlock4.png",
  },
];

export default function CoachInfoBlocks({ coachData }) {
  return (
    <div className="max-w-7xl mx-auto grid md:grid-cols-2 grid-cols-1 gap-10 py-2 px-5">
      {blocksData.map((block, index) => (
        <div
          key={index}
          className="bg-gradient-to-r from-red-100 to-sky-100 px-6 md:px-10 pt-4 md:pt-6 rounded-md flex flex-col gap-7 pb-14"
        >
          <div className="flex justify-between items-center md:items-end">
            <p className="md:text-2xl text-xl">{block.title}</p>
            <Image
              src={block.imageSrc}
              width={50}
              height={50}
              alt={`CoachBlock${index + 1}.png`}
              className="w-16"
            />
          </div>

          {coachData[block.key] && coachData[block.key].map((item, itemIndex) => (
            <div
              key={itemIndex}
              className="flex items-start justify-between text-lg line-clamp-2"
            >
              <div className="w-screen flex gap-4 items-baseline">
                <Image
                  src="/CoachDot.svg"
                  width={20}
                  height={20}
                  alt="CoachDot.svg"
                  className="w-3"
                />
                <p className="w-[90%] text-[#2B2B2A]"
                dangerouslySetInnerHTML={{ __html: item.description }}
                >
                
              </p>
                {/* <p className="text-sky-400">{item.year}</p> */}
              </div>
              
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}
