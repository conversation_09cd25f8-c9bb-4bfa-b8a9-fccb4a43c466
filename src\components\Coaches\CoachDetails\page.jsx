import Image from "next/image";
import Link from "next/link";

export default function CoachDetails({ coachData }) {
  const sportsCategoriesWithSpace = coachData?.sportsCategories?.map(category => category + ' ');
  return (
    <div className="max-w-7xl m-auto flex-col md:flex-row flex justify-between py-6 px-4">
      <div className="w-full md:w-[25%]">
        <Image
          src={coachData?.profileImg}
          width={200}
          height={200}
          alt="/Coach"
          className="w-full max-h-[20rem] object-cover rounded"
        />
        <div className="flex items-center justify-center w-full">
          <p className="text-center sm:text-lg">{`${coachData.firstName} ${coachData.lastName}`}</p>
          <Image
            src="/bluetick.png"
            width={20}
            height={20}
            alt="bluetick"
            className="w-10"
          />
        </div>
      </div>

      <div className="w-full md:w-[30%] flex flex-col gap-5 py-8">
        <div className="flex gap-6">
          <p className="text-sky-400 whitespace-nowrap">Profession :</p>
          {coachData.sportsCategories ?
          <p className="text-black">
             {`${sportsCategoriesWithSpace} Coach`}
          </p>
          :
          <p className="text-black">No Profession</p>
          }
        </div>
        <div className="flex gap-8">
          <p className="text-sky-400 whitespace-nowrap">Language :</p>
          {coachData.language ? 
          <p className="text-black"> {coachData.language}</p>
          :
          <p className="text-black">No language added</p>
}
        </div>
        <div className="flex gap-6">
          <p className="text-sky-400 whitespace-nowrap">Experience :</p>
          {coachData.experience ?
          <p className="text-black">{`${coachData.experience}+ Years`}</p>
          :
          <p className="text-black">No experience</p>
}
        </div>
      </div>

      <div className="w-full md:w-[30%] flex flex-col gap-5 py-8">
        <h3 className="text-[#000000]]">Venue</h3>
        {coachData.linkedFacilities && coachData.linkedFacilities.length > 0 ? (
          coachData.linkedFacilities.map((venue, index) => {
            return(
            <div className="flex gap-6 w-[100%] items-baseline" key={index}>
              <Image
                src="/CoachDot.svg"
                width={20}
                height={20}
                className="w-[3%]"
                alt="CoachDot"
              />
              <p className="w-[60%] text-sm">{venue.name} {venue.addressLine1} {venue.addressLine2} {venue.city}</p>
              {venue?.location?.coordinates[0] && venue.location?.coordinates[1] && (
              <Link target="_blank" href={`https://www.google.com/maps/search/?api=1&query=${venue?.location?.coordinates[0]},${venue?.location?.coordinates[1]}`} 
              className="text-sky-400 underline text-sm w-[30%]">
                Show on map
              </Link>
            )}
            </div>
          )})
        ) : (
          <div>No venue information available</div>
        )}
      </div>
    </div>
  );
}
