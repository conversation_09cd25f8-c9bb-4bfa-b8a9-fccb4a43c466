"use client";
import LeftDetails from "@/components/ThankYou/LeftDetails/page";
import RightDetails from "@/components/ThankYou/RightDetails/page";
import Image from "next/image";
import ReactToPrint from "react-to-print";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import Invoice from "@/components/Invoice/Invoice";
import CoachInvoice from "@/components/Invoice/CoachInvoice";
import OrderSummary from "@/components/OrderSummary/OrderSummary";
const navigation = {
  contact: [
    {
      name: "203, building 5,sector 4 Noida, Uttar pradesh",
      href: "#",
      svg: "/FooterLocation.svg",
    },
    { name: "+91 2134567890", href: "#", svg: "/FooterPhone.svg" },
    { name: "https://www.khelsports.in", href: "#", svg: "/FooterUrl.svg" },
  ],
  quick: [
    { name: "About Us", href: "/aboutus" },
    { name: "FAQ's", href: "/faqs" },
    { name: "Term of Service", href: "/termofservice" },
    { name: "Privacy Policy", href: "privacypolicy" },
    {
      name: "Become an Instructor",
      href: "https://3pd82u71m8.execute-api.ap-south-1.amazonaws.com/profile/basic_details#signup",
    },
  ],
  social: [
    {
      name: "Facebook",
      href: "https://www.facebook.com/akshaykumarofficial/",
      svg: "/ShareFacebook.svg",
    },
    {
      name: "Instagram",
      href: "https://www.instagram.com/akshaykumar/?hl=en",
      svg: "/ShareInstagram.svg",
    },
    {
      name: "Linkedin",
      href: "https://in.linkedin.com/in/akshay-kumar-15bb5b208",
      svg: "/ShareLinkedin.svg",
    },
    {
      name: "Twitter",
      href: "https://twitter.com/akshaykumar?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor",
      svg: "/ShareTwitter.svg",
    },
  ],
};

export default function Thankyou() {
  const pdref = useRef();
  const params = useSearchParams();
  const [invoiceShow, setInvoiceShow] = useState("khel");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isOverflowHidden, setIsOverflowHidden] = useState(false);
  const openModal = () => {
    setIsModalOpen(true);
    setIsOverflowHidden(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsOverflowHidden(false);
  };

  const [thankyouDetails, setThankyouDetails] = useState();
  let booking_id = params.get("booking_id");

  useEffect(() => {
    if (booking_id) {
      getUserToken(booking_id);
    }
  }, [booking_id]);

  async function getUserToken(booking_id) {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result = await response.json();
    if (result?.user?.token) {
      getBookingById(result.user.token, booking_id);
    }
  }

  async function getBookingById(token, booking_id) {
    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${booking_id}`,
      requestOptions
    )
      .then((response) => response.json())
      .then((result) => setThankyouDetails(result))
      .catch((error) => console.error(error));
  }
  return (
    <div className="bg-gradient-to-r from-gray-400 via-sky-300 to-sky-400 p-4 md:p-20 flex justify-between">
      <div className="hidden md:block w-3/12">
        <Image
          src="/thankyouImage.png"
          priority
          width={200}
          height={200}
          alt="thankyouImage"
          className="md:w-10/12 2xl:w-7/12"
        />
      </div>
      <div className="w-full py-6 px-4 md:py-6 md:px-8 shadow-md rounded-md bg-gray-100 md:w-10/12">
        <h2 className="text-2xl text-center">A GREAT BIG THANK YOU!</h2>
        <div className="flex flex-col md:flex-row justify-start">
          <p className="mt-4 w-full md:px-3 2xl:px-0 text-left text-sm md:text-lg font-bold">
            Khel Sports
          </p>
          <div className="flex flex-col md:flex-row  md:space-y-0 md:space-x-4 items-center">
            <button
              onClick={() => {
                setInvoiceShow("khel");
                openModal();
              }}
              className="text-left text-sm md:text-lg font-bold text-sky-400"
            >
              Platform Invoice
            </button>
            <button
              onClick={() => {
                setInvoiceShow("coach");
                openModal();
              }}
              className="text-left text-sm md:text-lg font-bold text-sky-400"
            >
              Coach Invoice
            </button>
            <button
              onClick={() => {
                setInvoiceShow("orderSummary");
                openModal();
              }}
              className="text-left text-sm md:text-lg font-bold text-sky-400"
            >
              Order Summary
            </button>
          </div>
        </div>



        {isModalOpen && (
          <div
            className={`fixed top-0 left-0 w-full h-full flex items-center justify-center bg-gray-500 bg-opacity-50 z-50 ${isOverflowHidden ? "overflow-hidden" : ""
              }`}
          >
            <div className="md:w-[60%] w-[95%] bg-white p-6 rounded-lg shadow-lg flex flex-col gap-4 max-h-[500px] lg:max-h-[700px] overflow-y-auto">
              <div ref={pdref}>
                {invoiceShow === "khel" ? (
                  <Invoice />
                ) : invoiceShow === "coach" ? (
                  <CoachInvoice />
                ) : invoiceShow === "orderSummary" ? (
                  <OrderSummary thankyouDetails={thankyouDetails} />
                ) : null}
              </div>
              <div className="flex justify-end gap-6">
                <ReactToPrint
                  trigger={() => <button colorScheme="twitter">Print</button>}
                  content={() => pdref.current}
                />
                <button onClick={closeModal}>Cancel</button>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col gap-5">
          <div className="flex gap-2">
            <Image
              src="/okIcon.svg"
              width={50}
              height={50}
              alt="roundedCheckbox"
              className="h-auto w-10"
            />
            <div>
              <p className="text-sky-400">Payment Confirmed!</p>
              <p>
                Ordered on{" "}
                {`${new Date(thankyouDetails?.createdAt).toLocaleDateString(
                  "en-IN",
                  { day: "numeric", month: "long", year: "numeric" }
                )}`}{" "}
                | Booking ID : {thankyouDetails?.bookingId}
              </p>
            </div>
          </div>
          <div className="md:flex justify-between w-full md:w-11/12 md:px-3 2xl:px-0 md:m-auto gap-10">
            <LeftDetails thankyouDetails={thankyouDetails} />
            <RightDetails thankyouDetails={thankyouDetails} />
          </div>
        </div>

        <div className="flex flex-wrap gap-6 md:gap-14 justify-center pt-[35px]">
          {navigation.social.map((social) => (
            <a
              key={social.name}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1"
            >
              <Image
                src={social.svg}
                width={20}
                height={20}
                alt={social.name}
                className="w-5"
              />
              <p className="text-gray-600">{social.name}</p>
            </a>
          ))}
        </div>
      </div>
    </div>
  );
}
