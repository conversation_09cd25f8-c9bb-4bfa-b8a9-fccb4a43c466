import Image from "next/image";

export default function BookingLeft({ bookingData }) {
  return (
    <div className="md:w-1/2 flex flex-col gap-4">
      <div className="flex gap-4 mb-4 justify-between md:justify-left">
        <h2 className=" text-[1.33rem]">{bookingData?.courseId?.courseName}</h2>
        {/* <div className={`flex items-center ${bookingData?.classes?.length < 2 && bookingData.classes[0].status !== "upcoming" ? "border-red-500 text-red-500" : "border-[#4CAF50] text-[#4CAF50]"} border  border-solid px-2 rounded capitalize`}>
          {bookingData?.classes?.length === 1 ? bookingData?.classes[0]?.status?.toUpperCase() : bookingData?.status === "Active" ? 'Active' :
            bookingData?.status === "Inactive" ? 'InActive' : bookingData?.status}
        </div> */}
        <div className={`border border-solid py-[2px] px-2 rounded-md capitalize ${bookingData?.status === "Active" ? 'text-[#4CAF50] border-[#4CAF50]' :
          bookingData?.status === "Inactive" ? 'text-[#FF0000] border-[#FF0000]' : 'text-[#4CAF50] border-[#4CAF50]'
          }`}>
          {bookingData?.status === "Active" ? 'Active' :
            bookingData?.status === "Inactive" ? 'Inactive' : bookingData?.status}
        </div>
      </div>
      <div>
        <Image
          src={bookingData?.courseId?.images[0]?.url}
          alt="bookingImage"
          height={500}
          width={500}
          className="w-full"
        />
      </div>
    </div>
  );
}
