import axios from "axios";
import { NextResponse } from "next/server";

export async function POST(req, res) {
  try {
    const reqBody = await req.json();
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/login`,
      reqBody,
      { headers: { "Content-Type": "application/json" } }
    );
    const token = response?.data?.token;
    const id = response?.data?.id;
    const resp = NextResponse.json({
      message: "Login Success",
      success: true,
    });
    resp.cookies.set("token", token, { httpOnly: true, secure: true });
    resp.cookies.set("player_id", id, { httpOnly: true, secure: true });
    return resp;
  } catch (error) {
    console.error("error", error?.response?.data?.message);
    return new NextResponse(JSON.stringify({ error: error?.response?.data }));
  }
}
