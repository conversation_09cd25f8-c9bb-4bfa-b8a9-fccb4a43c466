"use client";
import { Fragment, useState, useEffect } from "react";
import { Dialog, Disclosure, Transition, Menu } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { ChevronDownIcon, PlusIcon } from "@heroicons/react/20/solid";
import ListingCards from "../ListingCards/page";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import MultiRangeSlider from "multi-range-slider-react";
import "./PriceRangeSlider.css";
import axios from "axios";

const sortOptions = [
  { id: "ratings", name: "Best Rating", value: "des" },
  { id: "createdAt", name: "Newest", value: "asc" },
  { id: "fees", name: "Price: Low to High", value: "asc" },
  { id: "fees", name: "Price: High to Low", value: "des" },
];
const filters = [
  {
    id: "classType",
    name: "Class Type",
    options: [
      { value: "course", label: "Course" },
      { value: "class", label: "Class" },
    ],
  },
  {
    id: "sessionType",
    name: "Session Type",
    options: [
      { value: "individual", label: "Individual" },
      { value: "group", label: "Group" },
    ],
  },
  {
    id: "priceRange",
    name: "Price",
    options: [],
  },
  {
    id: "proficiency",
    name: "Proficiency Level",
    options: [
      { value: "beginner", label: "Beginner" },
      { value: "intermediate", label: "Intermediate" },
      { value: "advance", label: "Advance" },
    ],
  },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Filters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const [minValue, set_minValue] = useState(0);
  const [maxValue, set_maxValue] = useState(2500);
  const [filterSearchUrl, setFilterSearchUrl] = useState(
    searchParams.toString()
  );
  const [dynamicFilters, setDynamicFilters] = useState(filters);
  const fetchCategoriesData = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_BASE_URL}/api/category`);
      const sportsArray = response.data.data;

      const sortedSports = sportsArray.sort((a, b) => a.name.localeCompare(b.name));

      const fetchedCategories = sortedSports.map(cat => ({
        value: cat.name.toLowerCase(),
        label: cat.name,
      }));
      const existingCategoryFilter = filters.find(filter => filter.id === 'category');
      if (!existingCategoryFilter) {
        const NewFilters =
        {
          id: 'category',
          name: 'Categories',
          options: fetchedCategories,
        };
        filters.splice(3, 0, NewFilters)
        setDynamicFilters([...filters]);
      }
    } catch (error) {
      console.error('Error', error);
    }
  };
  useEffect(() => {
    fetchCategoriesData();
  }, []);
  const handleFilterTrigger = (key, value) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    const currentValues = newSearchParams.getAll(key);
    if (currentValues.includes(value)) {
      const updatedValues = currentValues.filter((v) => v !== value);
      newSearchParams.delete(key);
      if (updatedValues.length > 0) {
        updatedValues.forEach((updatedValue) => {
          newSearchParams.append(key, updatedValue);
        });
      }
    } else if (key === "priceRange") {
      newSearchParams.set("min", value.minValue);
      newSearchParams.set("max", value.maxValue);
    } else if (key === "ratings" || key === "createdAt" || key === "fees") {
      for (const existingvalue of searchParams.values()) {
        if (existingvalue === "asc" || existingvalue === "des") {
          newSearchParams.delete("ratings");
          newSearchParams.delete("createdAt");
          newSearchParams.delete("fees");
          newSearchParams.append(key, value);
        } else {
          newSearchParams.append(key, value);
        }
      }
    } else {
      newSearchParams.append(key, value);
    }

    const newUrl = `${pathname}?${newSearchParams.toString()}`;
    router.push(newUrl);
  };

  useEffect(() => {
    setFilterSearchUrl(searchParams.toString());
  }, [searchParams]);

  return (
    <div className="bg-white">
      <div>
        {/* Mobile filter dialog */}
        <Transition.Root show={mobileFiltersOpen} as={Fragment}>
          <Dialog
            as="div"
            className="relative z-40 lg:hidden"
            onClose={setMobileFiltersOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="transition-opacity ease-linear duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-linear duration-300"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-25" />
            </Transition.Child>

            <div className="fixed inset-0 z-40 flex">
              <Transition.Child
                as={Fragment}
                enter="transition ease-in-out duration-300 transform"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl">
                  <div className="flex items-center justify-between px-4">
                    <h2 className="text-lg font-medium text-gray-900">
                      Filters
                    </h2>
                    <button
                      type="button"
                      className="-mr-2 flex h-10 w-10 items-center justify-center p-2 text-gray-400 hover:text-gray-500"
                      onClick={() => setMobileFiltersOpen(false)}
                    >
                      <span className="sr-only">Close menu</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  {/* Filters */}
                  <form className="mt-4">
                    {dynamicFilters.map((section) => (
                      <Disclosure
                        as="div"
                        key={section.name}
                        className="border-t border-gray-200 pb-4 pt-4"
                      >
                        {({ open }) => (
                          <fieldset>
                            <legend className="w-full px-2">
                              <Disclosure.Button className="flex w-full items-center justify-between p-2 text-gray-400 hover:text-gray-500">
                                <span className="text-base font-medium text-black">
                                  {section.name}
                                </span>
                                <span className="ml-6 flex h-7 items-center">
                                  <ChevronDownIcon
                                    className={classNames(
                                      open ? "-rotate-180" : "rotate-0",
                                      "h-5 w-5 transform"
                                    )}
                                    aria-hidden="true"
                                  />
                                </span>
                              </Disclosure.Button>
                            </legend>
                            <Disclosure.Panel className="px-4 pb-2 pt-4">
                              <div className="space-y-6">
                                {section.options.map((option, optionIdx) => (
                                  <div
                                    key={option.value}
                                    className="flex items-center"
                                  >
                                    <input
                                      id={`${section.id}-${optionIdx}-mobile`}
                                      name={`${section.id}[]`}
                                      defaultValue={option.value}
                                      type="checkbox"
                                      onChange={() =>
                                        handleFilterTrigger(
                                          section.id,
                                          option.value
                                        )
                                      }
                                      checked={searchParams
                                        .getAll(section.id)
                                        .includes(option.value)}
                                      className="h-4 w-4 rounded border-gray-300 text-sky-600 focus:ring-sky-500"
                                    />
                                    <label
                                      htmlFor={`${section.id}-${optionIdx}-mobile`}
                                      className="ml-3 text-sm text-gray-500"
                                    >
                                      {option.label}
                                    </label>
                                  </div>
                                ))}

                                {section.id === "priceRange" && (
                                  <div className="pt-6">
                                    <h2 className="text-gray-500">
                                      Rs {minValue} - {maxValue}
                                    </h2>
                                    <MultiRangeSlider
                                      min={0}
                                      max={5000}
                                      minValue={minValue}
                                      maxValue={maxValue}
                                      onInput={(e) => {
                                        set_minValue(e.minValue);
                                        set_maxValue(e.maxValue);
                                      }}
                                      onChange={(e) => {
                                        handleFilterTrigger(section.id, e);
                                      }}
                                      ruler={false}
                                      label="Price Range"
                                      style={{
                                        boxShadow: "none",
                                        border: "none",
                                      }}
                                    />
                                  </div>
                                )}
                              </div>
                            </Disclosure.Panel>
                          </fieldset>
                        )}
                      </Disclosure>
                    ))}
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </Dialog>
        </Transition.Root>

        <main className="mx-auto max-w-2xl px-2 md:py-16 py-0 sm:px-6 sm:py-10 lg:max-w-7xl lg:px-8">
          <div className="flex items-baseline justify-around md:justify-between border-b border-gray-200 md:pb-4 py-[20px]">
            <div className="md:border-r-solid md:border-r-0 flex md:text-start items-center justify-center md:justify-start	  w-1/2 gap-2 border-r-solid border-r-[1.5px]">
              <svg className="md:hidden " width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clipPath="url(#clip0_16816_1723)">
                  <path d="M15.75 5.0625H6.1875C6.03832 5.0625 5.89524 5.00324 5.78975 4.89775C5.68426 4.79226 5.625 4.64918 5.625 4.5C5.625 4.35082 5.68426 4.20774 5.78975 4.10225C5.89524 3.99676 6.03832 3.9375 6.1875 3.9375H15.75C15.8992 3.9375 16.0423 3.99676 16.1477 4.10225C16.2532 4.20774 16.3125 4.35082 16.3125 4.5C16.3125 4.64918 16.2532 4.79226 16.1477 4.89775C16.0423 5.00324 15.8992 5.0625 15.75 5.0625Z" fill="#2B2B2A" />
                  <path d="M3.9375 5.0625H2.25C2.10082 5.0625 1.95774 5.00324 1.85225 4.89775C1.74676 4.79226 1.6875 4.64918 1.6875 4.5C1.6875 4.35082 1.74676 4.20774 1.85225 4.10225C1.95774 3.99676 2.10082 3.9375 2.25 3.9375H3.9375C4.08668 3.9375 4.22976 3.99676 4.33525 4.10225C4.44074 4.20774 4.5 4.35082 4.5 4.5C4.5 4.64918 4.44074 4.79226 4.33525 4.89775C4.22976 5.00324 4.08668 5.0625 3.9375 5.0625Z" fill="#2B2B2A" />
                  <path d="M11.8125 9.5625H2.25C2.10082 9.5625 1.95774 9.50324 1.85225 9.39775C1.74676 9.29226 1.6875 9.14918 1.6875 9C1.6875 8.85082 1.74676 8.70774 1.85225 8.60225C1.95774 8.49676 2.10082 8.4375 2.25 8.4375H11.8125C11.9617 8.4375 12.1048 8.49676 12.2102 8.60225C12.3157 8.70774 12.375 8.85082 12.375 9C12.375 9.14918 12.3157 9.29226 12.2102 9.39775C12.1048 9.50324 11.9617 9.5625 11.8125 9.5625Z" fill="#2B2B2A" />
                  <path d="M6.1875 14.0625H2.25C2.10082 14.0625 1.95774 14.0032 1.85225 13.8977C1.74676 13.7923 1.6875 13.6492 1.6875 13.5C1.6875 13.3508 1.74676 13.2077 1.85225 13.1023C1.95774 12.9968 2.10082 12.9375 2.25 12.9375H6.1875C6.33668 12.9375 6.47976 12.9968 6.58525 13.1023C6.69074 13.2077 6.75 13.3508 6.75 13.5C6.75 13.6492 6.69074 13.7923 6.58525 13.8977C6.47976 14.0032 6.33668 14.0625 6.1875 14.0625Z" fill="#2B2B2A" />
                  <path d="M5.0625 6.1875C4.72874 6.1875 4.40248 6.08853 4.12498 5.90311C3.84747 5.71768 3.63118 5.45413 3.50345 5.14578C3.37573 4.83743 3.34231 4.49813 3.40743 4.17079C3.47254 3.84344 3.63326 3.54276 3.86926 3.30676C4.10526 3.07076 4.40594 2.91004 4.73329 2.84493C5.06063 2.77981 5.39993 2.81323 5.70828 2.94095C6.01663 3.06868 6.28018 3.28497 6.46561 3.56248C6.65103 3.83998 6.75 4.16624 6.75 4.5C6.75 4.94755 6.57221 5.37678 6.25574 5.69324C5.93928 6.00971 5.51005 6.1875 5.0625 6.1875ZM5.0625 3.9375C4.95125 3.9375 4.84249 3.97049 4.74999 4.0323C4.65749 4.09411 4.58539 4.18196 4.54282 4.28474C4.50024 4.38752 4.4891 4.50062 4.51081 4.60974C4.53251 4.71885 4.58609 4.81908 4.66475 4.89775C4.74342 4.97642 4.84365 5.02999 4.95276 5.05169C5.06188 5.0734 5.17498 5.06226 5.27776 5.01968C5.38054 4.97711 5.46839 4.90501 5.5302 4.81251C5.59201 4.72001 5.625 4.61125 5.625 4.5C5.625 4.35082 5.56574 4.20774 5.46025 4.10225C5.35476 3.99676 5.21168 3.9375 5.0625 3.9375Z" fill="#2B2B2A" />
                  <path d="M12.9375 10.6875C12.6037 10.6875 12.2775 10.5885 12 10.4031C11.7225 10.2177 11.5062 9.95413 11.3785 9.64578C11.2507 9.33743 11.2173 8.99813 11.2824 8.67079C11.3475 8.34344 11.5083 8.04276 11.7443 7.80676C11.9803 7.57076 12.2809 7.41004 12.6083 7.34493C12.9356 7.27981 13.2749 7.31323 13.5833 7.44095C13.8916 7.56868 14.1552 7.78497 14.3406 8.06248C14.526 8.33998 14.625 8.66624 14.625 9C14.625 9.44755 14.4472 9.87678 14.1307 10.1932C13.8143 10.5097 13.3851 10.6875 12.9375 10.6875ZM12.9375 8.4375C12.8262 8.4375 12.7175 8.47049 12.625 8.5323C12.5325 8.59411 12.4604 8.68196 12.4178 8.78474C12.3752 8.88752 12.3641 9.00063 12.3858 9.10974C12.4075 9.21885 12.4611 9.31908 12.5398 9.39775C12.6184 9.47642 12.7186 9.52999 12.8278 9.55169C12.9369 9.5734 13.05 9.56226 13.1528 9.51968C13.2555 9.47711 13.3434 9.40501 13.4052 9.31251C13.467 9.22001 13.5 9.11125 13.5 9C13.5 8.85082 13.4407 8.70774 13.3352 8.60225C13.2298 8.49676 13.0867 8.4375 12.9375 8.4375Z" fill="#2B2B2A" />
                  <path d="M7.3125 15.1875C6.97874 15.1875 6.65248 15.0885 6.37498 14.9031C6.09747 14.7177 5.88118 14.4541 5.75345 14.1458C5.62573 13.8374 5.59231 13.4981 5.65743 13.1708C5.72254 12.8434 5.88326 12.5428 6.11926 12.3068C6.35526 12.0708 6.65594 11.91 6.98329 11.8449C7.31063 11.7798 7.64993 11.8132 7.95828 11.941C8.26663 12.0687 8.53018 12.285 8.71561 12.5625C8.90103 12.84 9 13.1662 9 13.5C9 13.9476 8.82221 14.3768 8.50574 14.6932C8.18928 15.0097 7.76005 15.1875 7.3125 15.1875ZM7.3125 12.9375C7.20125 12.9375 7.0925 12.9705 6.99999 13.0323C6.90749 13.0941 6.83539 13.182 6.79282 13.2847C6.75024 13.3875 6.7391 13.5006 6.76081 13.6097C6.78251 13.7189 6.83609 13.8191 6.91475 13.8977C6.99342 13.9764 7.09365 14.03 7.20276 14.0517C7.31188 14.0734 7.42498 14.0623 7.52776 14.0197C7.63054 13.9771 7.71839 13.905 7.7802 13.8125C7.84201 13.72 7.875 13.6113 7.875 13.5C7.875 13.3508 7.81574 13.2077 7.71025 13.1023C7.60476 12.9968 7.46169 12.9375 7.3125 12.9375Z" fill="#2B2B2A" />
                  <path d="M15.75 9.5625H14.0625C13.9133 9.5625 13.7702 9.50324 13.6648 9.39775C13.5593 9.29226 13.5 9.14918 13.5 9C13.5 8.85082 13.5593 8.70774 13.6648 8.60225C13.7702 8.49676 13.9133 8.4375 14.0625 8.4375H15.75C15.8992 8.4375 16.0423 8.49676 16.1477 8.60225C16.2532 8.70774 16.3125 8.85082 16.3125 9C16.3125 9.14918 16.2532 9.29226 16.1477 9.39775C16.0423 9.50324 15.8992 9.5625 15.75 9.5625Z" fill="#2B2B2A" />
                  <path d="M15.75 14.0625H8.4375C8.28832 14.0625 8.14524 14.0032 8.03975 13.8977C7.93426 13.7923 7.875 13.6492 7.875 13.5C7.875 13.3508 7.93426 13.2077 8.03975 13.1023C8.14524 12.9968 8.28832 12.9375 8.4375 12.9375H15.75C15.8992 12.9375 16.0423 12.9968 16.1477 13.1023C16.2532 13.2077 16.3125 13.3508 16.3125 13.5C16.3125 13.6492 16.2532 13.7923 16.1477 13.8977C16.0423 14.0032 15.8992 14.0625 15.75 14.0625Z" fill="#2B2B2A" />
                </g>
                <defs>
                  <clipPath id="clip0_16816_1723">
                    <rect width="18" height="18" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <h1
                onClick={() => setMobileFiltersOpen(true)}
                className="text-xl font-bold md:border-none tracking-tight text-gray-900"
              >
                Filters
              </h1>
            </div>
            <div className="flex w-1/2 justify-center md:justify-end items-center relative">
              <Menu as="div" className="md:relative inline-block text-left">
                <div>
                  <Menu.Button className="gap-2 group inline-flex justify-center items-center text-xl md:text-md font-medium text-gray-700 hover:text-gray-900">

                    <svg className="md:hidden" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5.24799 1.2998C5.24799 1.1672 5.30067 1.04002 5.39444 0.946251C5.48821 0.852483 5.61538 0.799805 5.74799 0.799805C5.8806 0.799805 6.00778 0.852483 6.10155 0.946251C6.19531 1.04002 6.24799 1.1672 6.24799 1.2998V13.5928C6.24797 13.6916 6.21867 13.7882 6.16379 13.8704C6.10891 13.9526 6.03091 14.0166 5.93965 14.0545C5.84838 14.0924 5.74793 14.1024 5.651 14.0832C5.55406 14.064 5.46497 14.0166 5.39499 13.9468L0.746991 9.2998C0.700428 9.25336 0.663485 9.19818 0.638279 9.13744C0.613073 9.07669 0.600098 9.01157 0.600098 8.9458C0.600098 8.88004 0.613073 8.81492 0.638279 8.75417C0.663485 8.69343 0.700428 8.63825 0.746991 8.5918H0.748992C0.842714 8.49837 0.969654 8.44591 1.10199 8.44591C1.23433 8.44591 1.36127 8.49837 1.45499 8.5918L5.24799 12.3878V1.2998ZM8.24799 2.0068C8.24821 1.90807 8.27765 1.81162 8.33261 1.7296C8.38756 1.64758 8.46557 1.58366 8.5568 1.54591C8.64803 1.50815 8.74839 1.49826 8.84523 1.51746C8.94208 1.53666 9.03107 1.5841 9.10099 1.6538L13.749 6.2998C13.7956 6.34625 13.8325 6.40143 13.8577 6.46217C13.8829 6.52292 13.8959 6.58804 13.8959 6.6538C13.8959 6.71957 13.8829 6.78469 13.8577 6.84544C13.8325 6.90618 13.7956 6.96136 13.749 7.0078H13.747C13.6533 7.10123 13.5263 7.1537 13.394 7.1537C13.2617 7.1537 13.1347 7.10123 13.041 7.0078L9.24799 3.2138V14.2998C9.24799 14.4324 9.19531 14.5596 9.10155 14.6534C9.00778 14.7471 8.8806 14.7998 8.74799 14.7998C8.61538 14.7998 8.48821 14.7471 8.39444 14.6534C8.30067 14.5596 8.24799 14.4324 8.24799 14.2998V2.0068Z" fill="#2B2B2A" />
                    </svg>

                    Sort
                    <ChevronDownIcon
                      className="-mr-1 ml-1 h-5 w-5 hidden md:flex flex-shrink-0 text-gray-400 group-hover:text-gray-500"
                      aria-hidden="true"
                    />
                  </Menu.Button>
                </div>

                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 z-10 mt-2 w-full md:w-40 origin-top-right rounded-md bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div className="py-1">
                      {sortOptions.map((option) => (
                        <Menu.Item key={option.name}>
                          {({ active }) => (
                            <div
                              className={classNames(
                                searchParams
                                  .getAll(option.id)
                                  .includes(option.value)
                                  ? "font-medium text-gray-900"
                                  : "text-gray-500",
                                active ? "bg-gray-100" : "",
                                "block px-4 py-2 text-base md:text-sm cursor-pointer"
                              )}
                              onClick={() =>
                                handleFilterTrigger(option.id, option.value)
                              }
                            >
                              {option.name}
                            </div>
                          )}
                        </Menu.Item>
                      ))}
                    </div>
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>

          <div className="pt-4 sm:pt-12 lg:grid lg:grid-cols-3 lg:gap-x-8 xl:grid-cols-4">
            <aside className="hidden md:block">
              <h2 className="sr-only">Filters</h2>

              <button
                type="button"
                className="inline-flex items-center lg:hidden"
                onClick={() => setMobileFiltersOpen(true)}
              >
                <span className="text-sm font-medium text-gray-700">
                  Filters
                </span>
                <PlusIcon
                  className="ml-1 h-5 w-5 flex-shrink-0 text-gray-400"
                  aria-hidden="true"
                />
              </button>

              <div className="hidden lg:block">
                <form className="space-y-10 divide-y divide-gray-200">
                  {dynamicFilters.map((section, sectionIdx) => (
                    <div
                      key={section.name}
                      className={sectionIdx === 0 ? null : "pt-10"}
                    >
                      {section.id === "priceRange" ? (
                        <fieldset>
                          <legend className="block text-sm font-medium text-gray-900">
                            {section.name}
                          </legend>
                          <div className="space-y-3 pt-6">
                            <h2 className="text-gray-500">
                              Rs {minValue} - {maxValue}
                            </h2>
                          </div>
                          <MultiRangeSlider
                            min={0}
                            max={5000}
                            minValue={minValue}
                            maxValue={maxValue}
                            onInput={(e) => {
                              set_minValue(e.minValue);
                              set_maxValue(e.maxValue);
                            }}
                            onChange={(e) => {
                              handleFilterTrigger(section.id, e);
                            }}
                            ruler={false}
                            label="Price Range"
                            style={{ boxShadow: "none", border: "none" }}
                          />
                        </fieldset>
                      ) : (
                        <fieldset>
                          <legend className="block text-sm font-medium text-gray-900">
                            {section.name}
                          </legend>
                          <div className="space-y-3 pt-6">
                            {section.options.map((option, optionIdx) => (
                              <div
                                key={option.value}
                                className="flex items-center"
                              >
                                <input
                                  id={`${section.id}-${optionIdx}`}
                                  name={`${section.id}[]`}
                                  defaultValue={option.value}
                                  type="checkbox"
                                  onChange={() =>
                                    handleFilterTrigger(
                                      section.id,
                                      option.value
                                    )
                                  }
                                  checked={searchParams
                                    .getAll(section.id)
                                    .includes(option.value)}
                                  className="h-4 w-4 rounded border-gray-300 text-sky-600 focus:ring-sky-500"
                                />
                                <label
                                  htmlFor={`${section.id}-${optionIdx}`}
                                  className="cursor-pointer	ml-3 text-sm text-gray-600"
                                >
                                  {option.label}
                                </label>
                              </div>
                            ))}
                          </div>
                        </fieldset>
                      )}
                    </div>
                  ))}
                </form>
              </div>
            </aside>

            {/* Product grid */}
            <div className="mt-6 lg:col-span-2 lg:mt-0 xl:col-span-3">
              <ListingCards filterSearchUrl={filterSearchUrl} />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
