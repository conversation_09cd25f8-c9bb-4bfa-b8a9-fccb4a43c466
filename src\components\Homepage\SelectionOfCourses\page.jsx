import Link from "next/link";
import Image from "next/image";
import Tabs from "../../Tabs/page";
const products = [
  {
    id: 1,
    name: "Course Name",
    href: "#",
    imageSrc: "/skate.png",
    imageAlt: "Front of men's Basic Tee in black.",
    price: "₹ 1,699",
    color: "Black",
  },
  {
    id: 1,
    name: "Course Name",
    href: "#",
    imageSrc: "/skate.png",
    imageAlt: "Front of men's Basic Tee in black.",
    price: "₹ 1,699",
    color: "Black",
  },
  {
    id: 1,
    name: "Course Name",
    href: "#",
    imageSrc: "/skate.png",
    imageAlt: "Front of men's Basic Tee in black.",
    price: "₹ 1,699",
    color: "Black",
  },
  {
    id: 1,
    name: "Course Name",
    href: "#",
    imageSrc: "/skate.png",
    imageAlt: "Front of men's Basic Tee in black.",
    price: "₹ 1,699",
    color: "Black",
  },
  // More products...
];

export default function SelectionOfCourses() {
  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 md:py-16 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
        <h2 className="text-2xl font-bold tracking-tight text-black">
          A BROAD SELECTION OF COURSES
        </h2>
        <Tabs/>
        <div className="mt-6 flex overflow-x-auto lg:overflow-x-hidden lg:grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8">
          {products.map((product, index) => (
            <div key={index} className="group relative w-9/12 lg:w-full flex-none lg:flex-auto mr-6">
              <div className="aspect-[3/2] w-full rounded-2xl object-cover">
                <Image
                  src={product.imageSrc}
                  alt={product.imageAlt}
                  width={500}
                  height={500}
                  className="w-100 h-auto rounded-lg object-cover"
                  style={{ width: "auto", height: "auto" }}
                />
              </div>
              <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                <div className="mt-4 flex justify-between items-center text-base not-italic font-medium">
                  <p className="text-white px-3.5 py-2 bg-red-600 rounded-lg">
                    {product.price}
                  </p>
                  <p className="text-sky-500">3 Classes/Week</p>
                </div>
                <div>
                  <h3 className="text-lg not-italic font-medium text-black">
                    <Link href={product.href}>
                      <span aria-hidden="true" />
                      {product.name}
                    </Link>
                  </h3>
                  <p className="mt-1 font-light text-gray-400 not-italic line-clamp-2">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit.
                    Dolorum neque molestias, reiciendis repellat quasi
                    similique, saepe voluptas natus maiores, blanditiis omnis
                    maxime? Aliquid aperiam mollitia soluta saepe libero nihil
                    amet.
                  </p>
                  <div className="mt-4 flex items-center underline gap-2">
                    <Link href="/product">What will you learn </Link>
                    <Image
                      src="/Arrow.svg"
                      alt="arrowsvg"
                      width={20}
                      height={20}
                      className="w-4 h-auto"
                      style={{ width: "auto", height: "auto" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
