.fromandinvoice {
  display: flex;
  justify-content: space-between;
  padding: 30px 20px 10px 20px;
}
.bordercolor {
  border: 2px solid rgb(4, 196, 225);
  height: 346px;
  width: 98%;
  margin: auto;
  background: rgb(240, 246, 247);
}
.onlyborder {
  border: 1px solid rgb(9, 205, 235);
}
.diagonal-border {
  width: 18%;
  height: 100%;
  background: linear-gradient(
    to right top,
    rgb(242, 60, 60) 50%,
    rgb(13, 192, 242) 50%
  );
  /* margin: auto; */
}
.diagonal_left {
  height: 100%;
  background-color: rgb(242, 60, 60);
  width: 38%;
}
.diagonal_right {
  height: 100%;
  background-color: rgb(13, 192, 242);
  width: 50%;
}
.diagonal_main {
  display: flex;
  height: 170px;
  margin-bottom: 30px;
}
table {
  border-collapse: collapse;
  border: 2px solid rgb(4, 196, 225);
  background: rgb(240, 246, 247);
  width: 95%;
  margin: auto;
}
th,
td {
  border: 2px solid rgb(4, 196, 225);
  padding: 8px;
  text-align: left;
}
tr:nth-child(2) {
  height: 200px;
}
th:nth-child(1) {
  width: 80%;
}
th:nth-child(2) {
  width: 20%;
}
