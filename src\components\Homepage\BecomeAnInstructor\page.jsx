import Image from "next/image";
import Link from "next/link";
export default function BecomeAnInstructor( {data, blockData }) {
  return (
    <div className="flex md:p-[32px] gap-6 md:gap-[20px] flex-col lg:flex-row max-w-7xl m-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="w-full lg:w-[55%]">
        <h3 className="text-[18px] lg:text-[38px]">{blockData?.title}</h3>
        <p className="mt-4 md:mt-6 md:text-lg text-[14px] enableLi"  dangerouslySetInnerHTML={{ __html: data[0]?.description }}/>
        <div className="mt-10 lg:mt-10 2xl:mt-10 flex items-center gap-6">
          <Link
            href={data[0]?.buttonUrl}
            className="py-2 px-4 bg-red-600 rounded-md text-white text-base"
          >
            Register Now
          </Link>
          <Link
            href="/registercoach"
            className="text-base underline flex items-center gap-2"
          >
            Read More
            <Image
              src="/Arrow.svg"
              alt="Arrow.svg"
              width={20}
              height={20}
              className="w-24 h-auto"
              style={{ width: "auto", height: "auto" }}
            />
          </Link>
        </div>
      </div>
      <div className="w-full lg:w-[45%] flex justify-center md:justify-end">
        <Image
          className="hidden lg:flex rounded-lg"
          priority
          src={data[0]?.image}
          alt="BecomeAnInstructor"
          width={500}
          height={500}
          style={{ width: "100%", height: "fit-content" }}
        />
        <Image
          className="flex lg:hidden rounded-lg"
          priority
          src={data[0]?.image}
          alt="BecomeAnInstructor"
          width={500}
          height={500}
          style={{ width: "100%", height: "auto" }}
        />
      </div>
    </div>
  );
}
