"use client";

import React, { useEffect } from "react";
import { useState } from "react";
import { Dialog } from "@headlessui/react";
import axios from "axios";

const TermsAndConditionModal = ({ open, setOpen, saveData, playerData }) => {
  //   const [open, setOpen] = useState(true);
console.log("--PLayer",playerData._id)
  const [policy, setPolicy] = useState("");

  const getPolicy = async () => {
    try {
      const data = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/cms/cms-player-termsAndCondition-details`
      );
      console.log(data, "policy");
      setPolicy(data?.data[0]?.termsAndConditionData);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getPolicy();
  }, [open, saveData]);

  return (
    <Dialog open={open} onClose={() => setOpen(!open)} className="relative z-10">
      <Dialog.Backdrop
        transition
        className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <Dialog.Panel
            transition
            className="relative transform overflow-hidden w-full rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-10 sm:w-full sm:max-w-3xl sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
          >
            <div className="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
              <h3 className="text-xl font-semibold text-gray-900">
                Terms of Service
              </h3>
              {/* <button
                type="button"
                className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                data-modal-hide="default-modal"
              >
                <svg
                  className="w-3 h-3"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 14 14"
                >
                  <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                  />
                </svg>
                <span className="sr-only" onClick={() => setOpen(!open)}>
                  Close modal
                </span>
              </button> */}
            </div>
            <p
            //   className="mt-3 text-sm text-gray-500"
              className="mt-3 mb-3 text-base leading-relaxed text-gray-500 dark:text-gray-400 max-h-[60vh] overflow-y-auto pr-2"
              dangerouslySetInnerHTML={{
                __html: policy,
              }}
            ></p>

            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
              <button
                data-modal-hide="default-modal"
                type="button"
                className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                onClick={() => {
                  saveData.handleSubmit();
                }}
              >
                I accept
              </button>
              {/* <button
                onClick={() => setOpen(!open)}
                data-modal-hide="default-modal"
                type="button"
                className="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              >
                Decline
              </button> */}
            </div>

            {/* <div className="mt-5 sm:mt-6 flex flex-row justify-between">
              <button
                type="button"
                onClick={() => setOpen(!open)}
                className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  setOpen(!open);
                  saveData.handleSubmit();
                }}
                className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Accept
              </button>
            </div> */}
          </Dialog.Panel>
        </div>
      </div>
    </Dialog>
  );
};

export default TermsAndConditionModal;
