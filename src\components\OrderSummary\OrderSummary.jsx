import React from "react";
import "./OrderSummary.css"; // Import your custom CSS

export default function OrderSummary({ thankyouDetails, closeSummary }) {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { day: "numeric", month: "long", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };

  const formatTime = (timeString) => {
    if (!timeString) {
      return "";
    }
    const [hours, minutes] = timeString.split(":");
    const time = new Date(1970, 0, 1, hours, minutes);
    return time.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
    });
  };
  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const coachGst = thankyouDetails?.coachId?.hasGst ? subtotal * 0.18 : 0;
  const platformFee = thankyouDetails?.academyId?.platformFee
    ? thankyouDetails.academyId.platformFee / 100
    : 0.12;

  const platformTax = subtotal * platformFee;

  // const platformTax = subtotal * 0.12;
  const taxGST = platformTax * 0.18;
  const total = Math.ceil(subtotal + platformTax + taxGST + coachGst);
  return (
    <div className="order-summary-container">
      <div className="order-summary-document">
        <div className="diagonal-main">
          <div className="diagonal-left"></div>
          <div className="diagonal-border"></div>
          <div className="diagonal-right">
            <p className="document-title">ORDER SUMMARY</p>
          </div>
        </div>
        <div className="summary-details">
          <div className="billing-info-container">
            <div className="billing-info-left">
              <p>Course Name: {thankyouDetails?.courseId?.courseName}</p>
              <p>Coach Name: {thankyouDetails?.courseId?.coachName}</p>
            </div>
            <div className="billing-info-right">
              <p>Invoice No: {thankyouDetails?.bookingId}</p>
              <p>
                Order Date:{" "}
                {new Date(thankyouDetails?.createdAt).toLocaleDateString(
                  "en-IN",
                  {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  }
                )}
              </p>
            </div>
          </div>

          <div className="summary-table">
            <table>
              <tr>
                <th>DESCRIPTION</th>
                <th>AMOUNT</th>
              </tr>
              <tr>
                <td>Subtotal ({thankyouDetails?.courseType})</td>
                <td>₹ {subtotal?.toFixed(2)}</td>
              </tr>
              <tr>
                <td>Platform Fee (12%)</td>
                <td>₹ {platformTax?.toFixed(2)}</td>
              </tr>
              <tr>
                <td>Total Taxes</td>
                <td>₹ {(taxGST + (coachGst || 0))?.toFixed(2)}</td>
              </tr>
              {/* <tr>
                <td>GST (18%)</td>
                <td>₹  {taxGST?.toFixed(2)}</td>
              </tr> */}
              {/* {thankyouDetails?.coachId?.hasGst && (
                <tr>
                  <td>Coach GST (18%)</td>
                  <td>₹ {thankyouDetails?.coachGst?.toFixed(2)}</td>
                </tr>
              )} */}
              <tr>
                <td className="total-label">TOTAL</td>
                <td>₹ {total}</td>
              </tr>
            </table>
          </div>
          <div className="booking-details">
            <h3>Booking Details</h3>
            {thankyouDetails?.classes?.map((x, index) => (
              <div className="booking-item" key={index}>
                <p>
                  Date:{" "}
                  {new Date(x.date).toLocaleDateString("en-IN", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}{" "}
                  ({x?.days})
                </p>
                <p>
                  Time:{" "}
                  {`${formatTime(x.startTime)} - ${formatTime(x.endTime)}`}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
