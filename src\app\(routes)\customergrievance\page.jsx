'use client'
import { useEffect, useState } from "react";

export default function CustomerGrievance() {
  const [customerGrievance, setCustomerGrievance] = useState(null);

  useEffect(() => {
    const fetchPrivacyPolicy = async () => {
      const requestOptions = {
        method: "GET",
        redirect: "follow"
      };

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/cms/policy`, requestOptions);
        const result = await response.json();
        console.log("-->result", result)
        setCustomerGrievance(result[0]?.customerGrievancePolicy);
      } catch (error) {
        console.error('Error fetching privacy policy:', error);
      }
    };

    fetchPrivacyPolicy();
  }, []);

  const createMarkup = (html) => {
    return { __html: html };
  };

  return (
    <div className="mx-auto max-w-7xl py-4 px-4 sm:py-6 sm:px-6 lg:py-8 lg:px-8">
      {customerGrievance && <div dangerouslySetInnerHTML={{ __html: customerGrievance }} />}
    </div>
  );
}
