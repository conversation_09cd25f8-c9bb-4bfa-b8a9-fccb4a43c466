"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import SuccessAlert from "./Alerts/SuccessAlert";
import ErrorAlert from "./Alerts/ErrorAlert";
const navigation = {
  contact: [
    {
      name: "Umn Khel Shiksha Private Limited,Vasant Vihar,Basant Lok Complex,Road 21,New Delhi-110057",
      href: "#",
      svg: "/FooterLocation.svg",
    },
    { name: "+91 92679 86189", href: "#", svg: "/FooterPhone.svg" },
    { name: "https://khelcoach.com", href: "https://khelcoach.com", svg: "/FooterUrl.svg" },
  ],
  quick: [
    { name: "About Us", href: "/aboutus" },
    { name: "FAQ's", href: "/faqs" },
    { name: "Term of Service", href: "/termofservice" },
    { name: "Privacy Policy", href: "privacypolicy" },
    { name: "Customer Grievance", href: "customergrievance" },
    // { name: "Support", href: "/contactus" },
    {
      name: "Register as a Coach",

      href: "https://coach.khelcoach.com/profile/basic_details",

    },
    {
      name: "Already a registered Coach, login",

      href: "https://coach.khelcoach.com/login",

    },
    {
      name: "Register as an Academy",

      href: "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com",

    },
    {
      name: "Already a registered Academy, login",

      href: "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com/login",

    },
  ],
  social: [
    {
      name: "Facebook",
      href: "https://facebook.com/61568900504191",
      svg: "/ShareFacebook.svg",
    },
    {
      name: "Instagram",
      href: "https://www.instagram.com/khelcoach/profilecard/?igsh=MW9wM3JwcDR3cmp1cA==                            ",
      svg: "/ShareInstagram.svg",
    },
    {
      name: "Linkedin",
      href: "https://www.linkedin.com/company/*********/admin/settings/manage-admins/",
      svg: "/ShareLinkedin.svg",
    },
    {
      name: "Twitter",
      href: "",
      svg: "/ShareTwitter.svg",
    },
  ],
};

export default function Footer() {
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

  const [email, setEmail] = useState("");
  const [errorMessage, setErrorMessage] = useState(false);

  const [successAlert, setSuccessAlert] = useState(false);
  const [errorAlert, setErrorAlert] = useState(false);

  const handleNewsLetterSubscription = async (e) => {
    e.preventDefault();
    try {
      if (!emailRegExp.test(email)) {
        setErrorMessage(true);
        return;
      }
      setErrorMessage(false);

      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ email }),
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/newsletter`,
        requestOptions
      );
      const result = await response.json();
      if (result.error) {
        setErrorAlert(true);
        setTimeout(() => {
          setErrorAlert(false);
        }, 3000);
      } else {
        setEmail("");
        setSuccessAlert(true);
        setTimeout(() => {
          setSuccessAlert(false);
        }, 3000);
      }
    } catch (error) {
      console.log(error, "error");
    }
  };

  return (
    <footer className="bg-white border-t" aria-labelledby="footer-heading">
      {successAlert && <SuccessAlert />}
      {errorAlert && <ErrorAlert />}

      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-12 pt-10 sm:pt-24 lg:px-8 lg:pt-12">
        <div className="flex flex-col-reverse xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 xl:col-span-2">
            <div className="border-t pt-4 md:border-none md:pt-0 md:grid md:grid-cols-1 md:gap-8">
              <div>
                <h3 className="text-lg md:text-xl font-semibold leading-6 text-black">
                  Contact Info
                </h3>
                <ul role="list" className="mt-4 space-y-4">
                  {navigation.contact.map((item) => (
                    <li key={item.name} className="flex items-center gap-2">
                      <Image
                        src={item.svg}
                        alt={item.name}
                        width={100}
                        height={100}
                        style={{ width: "8%", height: "auto" }}
                      />
                        {item.name === "https://khelcoach.com" ? <Link
                        href={item.href}
                        target="_blank"
                        className="text-base leading-6 text-gray-600 hover:text-gray-900"
                      >
                        {item.name}
                      </Link>: <div
                        // href={item.href}
                        // target="_blank"
                        className="text-base leading-6 text-gray-600 hover:text-gray-900"
                      >
                        {item.name}
                      </div>} 
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="border-t pt-4 md:border-none md:pt-0 md:grid md:grid-cols-1 md:gap-8">
              <div>
                <h3 className="text-lg md:text-xl font-semibold leading-6 text-black">
                  Quick Links
                </h3>
                <ul role="list" className="mt-4 space-y-2">
                  {navigation.quick.map((item) => (
                    <li key={item.name}>
                      <Link
                        target="_blank"
                        href={item.href}
                        className="text-base leading-6 text-gray-600 hover:text-gray-900"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="border-t pt-4 md:border-none md:pt-0 md:grid md:grid-cols-1 md:gap-8">
              <div>
                <h3 className="text-lg font-semibold leading-6 text-black">
                  Social Media
                </h3>
                <ul role="list" className="mt-4 space-y-4">
                  {navigation.social.map((item) => (
                    <li key={item.name} className="flex items-center gap-2">
                      <Image
                        src={item.svg}
                        alt={item.name}
                        width={50}
                        height={50}
                        className="w-5"
                      />
                      <Link
                        href={item.href}
                        target="_blank"
                        className="text-base leading-6 text-gray-600 hover:text-gray-900"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          <div className="xl:mt-0">
            <h3 className="mb-[12px] text-lg md:text-xl font-semibold leading-6 text-gray-900">
              Newsletter
            </h3>
            <p className="text-sm leading-6 text-gray-600">
              Subscribe to our updates
            </p>
            <form
              className="mt-2"
              onSubmit={(e) => handleNewsLetterSubscription(e)}
            >
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                type="text"
                name="email-address"
                id="email-address"
                autoComplete="email"
                value={email}
                // required
                onChange={(e) => setEmail(e.target.value)}
                className="w-full min-w-0 appearance-none rounded-sm border-0 bg-white px-3 py-1.5 text-base text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:w-64 sm:text-sm sm:leading-6 xl:w-full"
                placeholder="Enter your email"
              />
              {errorMessage && (
                <p className="mt-2 text-sm text-red-600">
                  {"Please enter a valid email address."}
                </p>
              )}
              <div className="mt-4">
                <button
                  type="submit"
                  className="flex w-full items-center justify-center rounded-md bg-red-600 px-3 py-2 text-base font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Subscribe
                </button>
              </div>
            </form>
            <div className="mt-6 mb-6">
              <h3 className="text-lg md:text-xl mb-[12px]">We Accept:</h3>
              <div className="flex items-center">
                <Image
                  src="/Visa.png"
                  alt="Visa"
                  width={100}
                  height={100}
                  style={{ width: "15%", height: "auto" }}
                />
                <Image
                  src="/MasterCard.png"
                  alt="MasterCard"
                  width={100}
                  height={100}
                  style={{ width: "15%", height: "auto" }}
                />
                <Image
                  src="/AmericanExpress.png"
                  alt="AmericanExpress"
                  width={100}
                  height={100}
                  style={{ width: "15%", height: "auto" }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
