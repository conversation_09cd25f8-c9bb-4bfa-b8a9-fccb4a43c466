"use client";
import Image from "next/image";
import { useState } from "react";

const Accordion = ({ detail, bookingArray, setBookingArray }) => {
  const [isOpen, setIsOpen] = useState(false);
  const handleDeleteSession = () => {
    const updatedBookingArray = bookingArray.filter(
      (session) =>
        session.date !== detail.date ||
        session.time !== detail.time
    );
    setBookingArray(updatedBookingArray);
  };
  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes < 10 ? "0" : ""}${minutes}${period}`;
  };
  
  const startTime = formatTime(detail.time.split(" to ")[0]);
  const endTime = formatTime(detail.time.split(" to ")[1]);
  const formattedTime = `${startTime} to ${endTime}`;
  return (
    <div className="border-b mt-4 pb-2">
      <p
        className="cursor-pointer flex items-center gap-4"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Image
          className={`transition-transform transform ${isOpen ? "rotate-90" : ""
            }`}
          src="/accordionArrow.svg"
          alt="Accordion Arrow"
          width={6}
          height={6}
        />
        <span className="w-[60%]">{new Date(detail.date).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: '2-digit' }).replace(/(\d+)(th|nd|st|rd)/, '$1<sup>$2</sup>')}</span>

        <div className="w-[20%] flex justify-end">
        <Image onClick={handleDeleteSession} src="/Binicon.svg" alt="Binicon" height={50} width={50} className="w-4"/></div>
      </p>
      {isOpen && (
        <div className="p-4">
          <p className="text-gray-700">{detail.time}</p>
        </div>
      )}
    </div>
  );
};

export default Accordion;
