import { <PERSON><PERSON> } from "next/font/google";
import "@/app/(routes)/globals.css";
import Layout from "@/components/Layout/page";
import { NextAuthProvider } from "./provider";

const inter = Lato({ weight: "400", subsets: ["latin"] });

//Meta Description including OG's, Canonical Tags
const meta = {
  title: process.env.NEXT_PUBLIC_TITLE,
  description: process.env.NEXT_PUBLIC_DESCRIPTION,
  image: `/MainLogo.svg`,
  url: process.env.NEXT_PUBLIC_BASE_URL,
};

export const metadata = {
  title: {
    template: "%s - Khel Sports",
    default: meta.title,
  },
  description: meta.description,
  openGraph: {
    title: meta.title,
    description: meta.description,
    url: meta.url,
    locale: "en-US",
    siteName: meta.title,
    type: "website",
    images: [
      {
        url: meta.image,
      },
    ],
  },
  twitter: {
    title: meta.title,
    description: meta.description,
    images: meta.image,
    card: "summary_large_image",
  },
  alternates: {
    canonical: meta.url,
  },
  metadataBase: process.env.NEXT_PUBLIC_BASE_URL,
};

//Organization Schema Seo
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: process.env.NEXT_PUBLIC_ORGANIZATION,
  logo: process.env.NEXT_PUBLIC_LOGO,
  sameAs: process.env.NEXT_PUBLIC_SAME_AS,
  url: process.env.NEXT_PUBLIC_WEB_URL
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
      <link rel="icon" href="/favicon.png" type="image/x-icon" sizes="any" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <script async src="https://checkout.razorpay.com/v1/checkout.js"></script>
      <body className={inter.className}>
        <NextAuthProvider>
          <Layout>{children}</Layout>
        </NextAuthProvider>
      </body>
    </html>
  );
}
