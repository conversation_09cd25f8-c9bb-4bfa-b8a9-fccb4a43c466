import { NextResponse } from "next/server";
import { verifyToken } from "@/app/helpers/getDataFromToken";

export async function POST(req, res) {
  try {
    const token = await req.cookies.get("token")?.value || "";
    let resp
    if (token) {
      const payload = await verifyToken(token);
      const player_id = payload.id;
      return resp = NextResponse.json({
        user: token ? { token: token, player_id: player_id, success: true } : false,
      });
    }
    return new NextResponse(JSON.stringify({ message: "Token Not Found" }));
  } catch (error) {
    console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}