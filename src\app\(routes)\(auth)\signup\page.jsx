"use client";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useFormik } from "formik";
import * as Yup from "yup";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import MultiSelectorDropdown from "@/components/MultiSelectorDropdown/page";
import { signIn, useSession } from "next-auth/react";
import { UsersIcon } from "@heroicons/react/24/outline";
import { Country, State, City } from 'country-state-city';

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
const signUpSchema = Yup.object({
  fname: Yup.string()
    .trim()
    .min(3, "First name must be at least 3 character")
    .max(15, "Cannot exceed 15 characters")
    .required("Please enter your first name"),
  lname: Yup.string()
    .trim()
    .min(3, "Last name must be at least 3 characters")
    .max(15, "Cannot Exceed 15 Characters")
    .required("Please enter your last name"),
  mobile: Yup.string()
    .min(10, "Min Limit is 10 characters")
    .max(10, "Max Limit is 10 characters")
    .matches(phoneRegExp, "Phone number is not valid")
    .required("Please enter your number"),
  email: Yup.string()
    .email("Please Enter a Valid Email")
    .matches(emailRegExp, "Please Enter a Valid Email")
    .required("Please enter your email"),
  homeState: Yup.string()
    .required("GST State is required")
    .max(100, "Only 100 characters are allowed"),
  school: Yup.string()
    .min(4, "School Must be atleast 4 characters")
    .max(50, "Cannot Exceed 50 Characters"),
  password: Yup.string()
    .matches(
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*]).{8,}$/,
      "Password must contain minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character"
    )
    .required("Please enter your password"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password"), null], "Passwords must match")
    .required("Please confirm your password"),
});

const initialValues = {
  fname: "",
  lname: "",
  mobile: "",
  email: "",
  school: "",
  homeState: "",
  password: "",
  confirmPassword: "",
};

const Loader = () => (
  <svg
    className="animate-spin h-5 w-5 mr-3 border-b-2 border-white rounded-full"
    viewBox="0 0 24 24"
  ></svg>
);

export default function SignUp() {
  const session = useSession();
  const router = useRouter();
  const [AlreadyRegistered, setAlreadyRegistered] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setConfirmShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [mobileError, setMobileError] = useState(false);
  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      validationSchema: signUpSchema,
      onSubmit: (values) => {
        setIsLoading(true);
        setAlreadyRegistered("");
        const selectedOptionIds = selectedOptions?.map((selectedOption) => {
          const matchedOption = options?.data?.find(
            (option) => option.name === selectedOption
          );
          return matchedOption ? { id: matchedOption._id } : null;
        });
        const userObj = {
          firstName: values.fname,
          lastName: values.lname,
          mobile: values.mobile,
          email: values.email,
          homeState: values.homeState,
          schoolName: values.school,
          hobbies: selectedOptionIds.filter((id) => id !== null),
          password: values.password,
          confirmPassword: values.confirmPassword,
        };
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        let requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: JSON.stringify(userObj),
          redirect: "follow",
        };
        fetch("/api/signup", requestOptions)
          .then((response) => response.json())
          .then((result) => {
            if (result?.error === "Invalid phone number") {
              setMobileError("Invalid phone number");
              setIsLoading(false);
            } else if (
              result?.error ===
              "Player with same mobile or email already exists"
            ) {
              setAlreadyRegistered(
                "Player with same mobile or email already exists"
              );
              setIsLoading(false);
            } else {
              localStorage.getItem("Booked")
                ? router.push(`/courses/${localStorage.getItem("Booked")}`)
                : router.push("/");
            }
          })
          .catch((error) => {
            console.log("error 122");
          });
      },
    });

  const [states, setStates] = useState([]);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const [options, setOptions] = useState([]);
  useEffect(() => {
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };

    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/category`, requestOptions)
      .then((response) => response.json())
      .then((result) => setOptions(result))
      .catch((error) => console.error(error));
  }, []);
  if (session.status == "authenticated") {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    let emailObj = {
      email: session.data.user.email,
      firstName: session.data.user.name.split(" ")[0],
      lastName: session.data.user.name.split(" ")[1],
    };
    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify(emailObj),
      redirect: "follow",
    };
    fetch(`/api/google`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result?.success) {
          localStorage.getItem("Booked")
            ? router.push(`/courses/${localStorage.getItem("Booked")}`)
            : router.push("/account");
        } else {
          console.log("check code 160");
        }
      })
      .catch((error) => {
        console.log("error 164");
      });
  } else if (session.status == "unauthenticated") {
    document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
    document.cookie =
      "player_id=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
  }

  const [selectedOptions, setSelectedOptions] = useState([]);

  return (
    <div className="w-full px-4 py-8 lg:py-16 sm:px-0 md:px-4">
      <div className="border py-4 sm:w-6/12 lg:w-4/12 2xl:w-3/12 shadow rounded-lg mx-auto">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <Image
            priority
            className="mx-auto h-15 md:w-[152px] w-[104px]"
            src="/MainKhelCoach.png"
            alt="Your Company"
            width={100}
            height={100}
          />
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            Sign up for your account
          </h2>
        </div>

        <div className="mt-10 w-11/12 m-auto">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="flex-col gap-4 sm:flex-row sm:gap-6 flex justify-between">
              <div className="w-full">
                <label
                  htmlFor="fname"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  First Name*
                </label>
                <div className="mt-2">
                  <input
                    id="fname"
                    name="fname"
                    type="fname"
                    autoComplete="fname"
                    placeholder="Enter First Name"
                    value={values.fname}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                  ${
                    errors.fname && touched.fname
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                  />
                  {errors.fname && touched.fname ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.fname}
                    </span>
                  ) : null}
                </div>
              </div>

              <div className="w-full">
                <label
                  htmlFor="lname"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Last Name*
                </label>
                <div className="mt-2">
                  <input
                    id="lname"
                    name="lname"
                    type="lname"
                    autoComplete="lname"
                    placeholder="Enter Last Name"
                    value={values.lname}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                  ${
                    errors.lname && touched.lname
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                  />
                  {errors.lname && touched.lname ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.lname}
                    </span>
                  ) : null}
                </div>
              </div>
            </div>

            <div className="flex-col gap-4 sm:flex-row sm:gap-6 flex justify-between">
              <div className="w-full">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Email address*
                </label>
                <div className="mt-2">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    value={values.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter Email Password"
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                ${
                  errors.email && touched.email
                    ? "border-2 border-rose-500"
                    : ""
                }`}
                  />
                  {errors.email && touched.email ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.email}
                    </span>
                  ) : null}
                  {/* {AlreadyRegistered && (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                    {AlreadyRegistered}
                  </span>
                  )} */}
                </div>
              </div>
              <div className="w-full">
                <label
                  htmlFor="mobile"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Enter Mobile Number*
                </label>
                <div className="mt-2">
                  <div className="flex">
                    <span className="px-1 py-1.5 border border-gray-300 bg-gray-100 rounded-l-md rounded-r-none text-gray-500">
                      +91
                    </span>
                    <input
                      id="mobile"
                      name="mobile"
                      type="number"
                      autoComplete="mobile"
                      placeholder="Enter Mobile Number"
                      value={values.mobile}
                      onChange={(e) => {
                        setMobileError(false);
                        handleChange(e);
                      }}
                      onBlur={handleBlur}
                      className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6 ${
                        errors.mobile && touched.mobile
                          ? "border-2 border-rose-500"
                          : ""
                      }`}
                    />
                  </div>

                  {mobileError && (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {mobileError}
                    </span>
                  )}
                  <style jsx>{`
                    input[type="number"]::-webkit-inner-spin-button,
                    input[type="number"]::-webkit-outer-spin-button {
                      -webkit-appearance: none;
                      margin: 0;
                    }

                    input[type="number"] {
                      -moz-appearance: textfield;
                    }
                  `}</style>
                  {errors.mobile && touched.mobile ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.mobile}
                    </span>
                  ) : null}
                </div>
              </div>
            </div>

            <div className="flex-col gap-4 sm:flex-row sm:gap-6 flex justify-between">
              <div className="w-full">
                <label
                  htmlFor="school"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  School/College
                </label>
                <div className="mt-2">
                  <input
                    id="school"
                    name="school"
                    type="school"
                    autoComplete="school"
                    placeholder="Enter School/College"
                    value={values.school}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                    ${
                      errors.school && touched.school
                        ? "border-2 border-rose-500"
                        : ""
                    }`}
                  />
                  {errors.school && touched.school ? (
                    <span className="form_errors text-sm font-medium leading-6 text-red-500">
                      {errors.school}
                    </span>
                  ) : null}
                </div>
              </div>
              <div className="w-full">
                <label
                  htmlFor="school"
                  className="block text-sm font-medium leading-6 text-gray-900 "
                >
                  Hobbies
                </label>
                <MultiSelectorDropdown
                  options={options?.data?.map((item) => item.name)}
                  selectedOptions={selectedOptions}
                  onChange={setSelectedOptions}
                />
              </div>
            </div>
            <div className="sm:col-span-3">
              <label
                htmlFor="homeState"
                className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
              >
                Your Home State*
              </label>
              <div className="mt-2">
                <select
                  name="homeState"
                  id="homeState"
                  value={values.homeState}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6
      ${
        touched.homeState && errors.homeState ? "border-2 border-rose-500" : ""
      }`}
                >
                  <option value="">Select State</option>
                  {states.map((state) => (
                    <option key={state.isoCode} value={state.isoCode}>
                      {state.name}
                    </option>
                  ))}
                </select>
                {touched.homeState && errors.homeState && (
                  <div className="text-red-500">{errors.homeState}</div>
                )}
              </div>
            </div>

            <div className="flex-col gap-4 sm:flex-row sm:gap-6 flex justify-between">
              <div className="w-full">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Password*
                </label>
                <div className="relative">
                  <input
                    placeholder="Enter Password"
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="current-password"
                    value={values.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                ${
                  errors.password && touched.password
                    ? "border-2 border-rose-500"
                    : ""
                }`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg
                        className="h-4 text-gray-700 cursor-pointer"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 576 512"
                      >
                        <path
                          fill="currentColor"
                          d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"
                        ></path>
                      </svg>
                    ) : (
                      <svg
                        className="h-4 text-gray-700 cursor-pointer"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 640 512"
                      >
                        <path
                          fill="currentColor"
                          d="M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z"
                        ></path>
                      </svg>
                    )}
                  </button>
                </div>
                {errors.password && touched.password ? (
                  <span className="form_errors text-sm font-medium leading-6 text-red-500">
                    {errors.password}
                  </span>
                ) : null}{" "}
              </div>
              <div className="w-full">
                <label
                  htmlFor="confirmpassword"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Confirm Password*
                </label>
                <div className="relative">
                  <input
                    placeholder="Confirm Password"
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    autoComplete="confirm-password"
                    value={values.confirmPassword}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                ${
                  errors.confirmPassword && touched.confirmPassword
                    ? "border-2 border-rose-500"
                    : ""
                }`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    onClick={() => setConfirmShowPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg
                        className="h-4 text-gray-700 cursor-pointer"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 576 512"
                      >
                        <path
                          fill="currentColor"
                          d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"
                        ></path>
                      </svg>
                    ) : (
                      <svg
                        className="h-4 text-gray-700 cursor-pointer"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 640 512"
                      >
                        <path
                          fill="currentColor"
                          d="M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z"
                        ></path>
                      </svg>
                    )}
                  </button>
                </div>
                {errors.confirmPassword && touched.confirmPassword ? (
                  <span className="form_errors text-sm font-medium leading-6 text-red-500">
                    {errors.confirmPassword}
                  </span>
                ) : null}{" "}
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
              >
                {isLoading ? <Loader /> : "Sign Up"}
              </button>
              {AlreadyRegistered && (
                <p className="text-red-500 text-center text-base">
                  {AlreadyRegistered}
                </p>
              )}
            </div>

            <div className="mt-10">
              <div className="relative">
                <div
                  className="absolute inset-0 flex items-center"
                  aria-hidden="true"
                >
                  <div className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center text-sm font-medium leading-6">
                  <span className="bg-white px-6 text-gray-900">
                    Or continue with
                  </span>
                </div>
              </div>
            </div>
          </form>
          <div className="mt-6">
            <button
              onClick={() => signIn("google", { prompt: "select_account" })}
              className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
            >
              <Image
                className="w-5"
                src="/google.svg"
                height={50}
                width={50}
                alt="googleSvg"
              />
              <span className="text-sm font-semibold leading-6">Google</span>
            </button>
          </div>
          <p className="mt-10 text-center text-sm text-gray-500">
            Already Member?
            <Link
              href="/login"
              className="font-semibold leading-6 text-sky-500 hover:text-sky-400"
            >
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
