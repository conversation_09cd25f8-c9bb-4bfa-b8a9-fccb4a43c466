export default function CourseDescription({ data }) {
  return (
    <>
     { data.description? 
     <div className="mt-14 productdescriptions">
        <h2 className="text-lg md:text-2xl">Description</h2>
        <p className="text-sm md:text-base mt-2 md:mt-6 text-gray-400" dangerouslySetInnerHTML={{ __html: data.description }} />
      </div>
    :<div className="mt-14 productdescriptions">
    <h2 className="text-lg md:text-2xl">Description</h2>
    <p className="text-sm md:text-base mt-2 md:mt-6 text-gray-400" dangerouslySetInnerHTML={{ __html: data.description }} />
  </div>  
    }

     { data.cancellationPolicy ?
     <div className="mt-14 productdescriptions p-10 bg-black text-white rounded-lg">
      <h2 className="text-lg md:text-xl">CANCELLATION POLICY</h2>
        <p className="text-sm md:text-base mt-2 md:mt-6 text-gray-200" dangerouslySetInnerHTML={{ __html: data.cancellationPolicy }} />
      </div>
    : ''  
    }
    </>
  );
}
