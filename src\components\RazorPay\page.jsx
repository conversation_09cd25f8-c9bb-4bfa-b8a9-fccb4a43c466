"use client";
import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import moment from "moment-timezone";
import PriceBreakdownModal from "./PriceBreakDownModal/page";
import { AlertModal } from "../AlertModal";
import TermsAndConditionModal from "../TermsAndCondition/TermsAndConditionModal";
export default function RazorPay({
  data,
  finalPrice,
  playerData,
  bookingArray,
  setBookingArray,
}) {
  const router = useRouter();
    const params = useParams();
  const [isWalletChecked, setIsWalletChecked] = useState(false);
  const [walletPrice, setWalletPrice] = useState(0);
  const [isAlreadyBooked, setIsAlreadyBooked] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showPriceSummary, setShowPriceSummary] = useState(false);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  const handleWalletCheckboxChange = () => {
    setIsWalletChecked(!isWalletChecked);
  };
  const [showModal, setShowModal] = useState(false);
  const [alertMessage, setAlertMessage] = useState(null);
  useEffect(() => {
    async function fetchWalletData() {
      let requestOptions = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        redirect: "follow",
      };
      const response = await fetch("/api/profile", requestOptions);
      const result = await response.json();
      if (result?.user?.token) {
        setLoading(true);
        try {
          const requestOptions = {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${result?.user?.token}`,
            },
            redirect: "follow",
          };
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/wallet?email=${playerData?.email}`,
            requestOptions
          );
          const result1 = await response.json();
          if (result1.data.length > 0)
            setWalletPrice(result1?.data[0]?.balance);
        } catch (error) {
          console.error("Error fetching wallet data:", error);
        }
        const requestOptions = {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${result?.user?.token}`,
          },
          redirect: "follow",
        };

        fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking?playerId=${playerData?._id}&courseId=${data._id}`,
          requestOptions
        )
          .then((response) => response.json())
          .then((result) => {
            setIsAlreadyBooked(result.data);
            setLoading(false);
          })
          .catch((error) => console.error(error));
      }
    }
    if (playerData?._id) {
      fetchWalletData();
    }
  }, [playerData]);

  const handlePriceSummary = async () => {
    if (playerData?.privacyPolicyAccepted === false ){
      setIsTermsModalOpen(true); 
      return;
    }else{
      setShowPriceSummary(true);
    }
    // setShowPriceSummary(true);
  };

  const handleRazorPay = async () => {
    try {
      const SessionEvents = bookingArray.map((x) => {
        const startTime = convertTimeTo24HourFormat(x.time.split(" to ")[0]);
        const endTime = convertTimeTo24HourFormat(x.time.split(" to ")[1]);
        return {
          date: moment
            .tz(`${x.date}T${startTime}:00.000`, "Asia/Kolkata")
            .format(),
          duration: x.session,
          days: x.day,
          startTime: startTime,
          endTime: endTime,
          fees:
            x.session == "30 mins"
              ? data.fees.fees30
              : x.session == "45 mins"
              ? data.fees.fees45
              : data.fees.fees60,
        };
      });

      const response = await fetch("/api/profile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        redirect: "follow",
      });

      const result = await response.json();
      if (result.user) {
        let raw = {
          pricePaid: finalPrice,
          currency: "INR",
          coachId: data?.coach_id?._id,
          courseId: data?._id,
          player: playerData?._id,
          courseType: data?.classType,
          groupSize: data?.maxGroupSize,
          wallet: isWalletChecked,
        };

        if (data?.classType === "class") {
          raw.classes = SessionEvents;
        }
        let requestOptions = {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          redirect: "follow",
        };
        const response = await fetch("/api/profile", requestOptions);
        const result = await response.json();
        if (result?.user?.token) {
          const requestOptions = {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${result?.user?.token}`,
            },
            body: JSON.stringify(raw),
            redirect: "follow",
          };
          const paymentResponse = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/payments/checkout`,
            requestOptions
          );
          const paymentResult = await paymentResponse.json();
          if (paymentResult?.error) {
            alert(`Can't Book, ${paymentResult?.error}`);
          } else if (paymentResult?.data?.order == "Not Required") {
            window.location.href = `/thankyou?booking_id=${paymentResult?.booking?.data?._id}`;
          } else {
            const options = {
              key: process.env.RAZORPAY_KEY,
              amount: finalPrice,
              currency: "INR",
              name: "KhelSports",
              description: `${data.courseName} ${data.classType} Transaction`,
              image: data?.images[0]?.url,
              order_id: paymentResult.data.id,
              handler: async function (response) {
                const requestOptions1 = {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${result?.user?.token}`,
                  },
                  body: JSON.stringify(response),
                  redirect: "follow",
                };
                const paymentVerification = await fetch(
                  `${process.env.NEXT_PUBLIC_BASE_URL}/api/payments/paymentverification`,
                  requestOptions1
                );
                const verifyResult = await paymentVerification.json();
                if (paymentVerification.status === 200) {
                  setBookingArray([]);
                  window.location.href = `/thankyou?booking_id=${verifyResult._id}`;
                } else {
                  setShowModal(!showModal);
                  setAlertMessage(
                    `${verifyResult?.message}: Please try again. If payment has been made, the money will be refunded.`
                  );
                }
              },
              prefill: {
                name: `${playerData.firstName} ${playerData.lastName}`,
                email: playerData.email,
                contact: playerData.mobile,
              },
              notes: {
                address: "Razorpay Corporate Office",
              },
              theme: {
                color: "#3399CC",
              },
            };
            const rzp1 = new window.Razorpay(options);
            rzp1.open();
          }
        }
      } else {
        localStorage.setItem("Booked", params.handle);
        router.push("/login");
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const convertTimeTo24HourFormat = (time) => {
    const [hourMinute, period] = time.split(/\s+/);
    const [hour, minute] = hourMinute.split(":");
    let newHour = parseInt(hour);

    if (period === "PM" && newHour !== 12) {
      newHour += 12;
    } else if (period === "AM" && newHour === 12) {
      newHour = 0;
    }

    return `${newHour.toString().padStart(2, "0")}:${minute.replace(
      /\D/g,
      ""
    )}`;
  };
  const handleAcceptPolicy = () => {

    setIsTermsModalOpen(false);
    setShowPriceSummary(true);
  };
  return (
    <>
      <AlertModal
        show={showModal}
        onClose={() => setShowModal(!showModal)}
        message={alertMessage}
      />
      {isTermsModalOpen && (
          <TermsAndConditionModal
            open={isTermsModalOpen}
            setOpen={setIsTermsModalOpen}
            saveData={{ handleSubmit: handleAcceptPolicy }}
            playerData={playerData}
          />
        )}
      {showPriceSummary && (
        <PriceBreakdownModal
          data={data}
          setShowPriceSummary={setShowPriceSummary}
          handleRazorPay={handleRazorPay}
          bookingArray={bookingArray}
          playerData={playerData}
        />
      )}
      {loading ? (
        <div role="status" className="flex justify-center items-center">
          <svg
            aria-hidden="true"
            className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-blue-500"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
          <span className="sr-only">Loading...</span>
        </div>
      ) : (
        <>
          {data?.classType === "course" &&
          data?.playerEnrolled < data?.maxGroupSize ? (
            <div className="flex gap-2">
              <input
                disabled={walletPrice < 1}
                type="checkbox"
                checked={isWalletChecked}
                onChange={handleWalletCheckboxChange}
              />
              <div className="flex items-center gap-1">
                <p className="text-base">Use Wallet </p>
                <p className="text-sm text-gray-500">
                  (Bal:{" "}
                  {walletPrice < 1 ? walletPrice : walletPrice?.toFixed(2)})
                </p>
              </div>
            </div>
          ) : (
            data?.classType === "class" && (
              <div className="flex gap-2">
                <input
                  disabled={walletPrice < 1}
                  type="checkbox"
                  checked={isWalletChecked}
                  onChange={handleWalletCheckboxChange}
                />
                <div className="flex items-center gap-1">
                  <p className="text-base">Use Wallet </p>
                  <p className="text-sm text-gray-500">
                    (Bal:{" "}
                    {walletPrice < 1 ? walletPrice : walletPrice?.toFixed(2)})
                  </p>
                </div>
              </div>
            )
          )}
          <button
            className={`w-full py-3.5 px-8 ${
              data.classType == "course"
                ? data?.playerEnrolled == data?.maxGroupSize ||
                  isAlreadyBooked?.length > 0
                  ? "bg-red-300"
                  : "bg-red-600"
                : "bg-red-600"
            } mt-2 text-white rounded-lg`}
            onClick={handlePriceSummary}
            disabled={
              data.classType == "course"
                ? data?.playerEnrolled == data?.maxGroupSize ||
                  isAlreadyBooked?.length > 0
                  ? true
                  : false
                : false
            }
          >
            {data.classType == "course"
              ? data?.playerEnrolled == data?.maxGroupSize ||
                isAlreadyBooked?.length > 0
                ? "Slot Unavailable"
                : "Pay Now"
              : "Pay Now"}
          </button>
          
        </>
      )}
    </>
  );
}
