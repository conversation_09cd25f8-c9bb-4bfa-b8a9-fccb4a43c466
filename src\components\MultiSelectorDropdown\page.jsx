import React, { useState, useEffect, useRef } from "react";

const MultiSelectorDropdown = ({ options, selectedOptions, onChange }) => {
  const [optionsValue, setOptionsValue] = useState();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionToggle = (option) => {
    const updatedOptions = selectedOptions.includes(option)
      ? selectedOptions.filter((item) => item !== option)
      : [...selectedOptions, option];
    onChange(updatedOptions);
    setOptionsValue(updatedOptions);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div
      className="relative inline-block text-left mt-2 w-full"
      ref={dropdownRef}
    >
      <div>
        <span className="rounded-md shadow-sm">
          <input
            type="text"
            readOnly
            className="inline-flex justify-center w-full rounded-md border border-gray-300 px-4 py-2 bg-white text-sm font-medium text-gray-400 hover:bg-gray-50 focus:outline-none focus:ring focus:border-blue-300"
            id="options-menu"
            onClick={handleToggle}
            value={
              selectedOptions.length > 0
                ? selectedOptions.join(", ")
                : "Select Options"
            }
          />
        </span>
      </div>

      {isOpen && (
        <div className="origin-top-right absolute left-0 mt-2 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-[1]">
          <div className="py-1">
            {options.map((option) => (
              <div
                key={option}
                className="flex items-center px-4 py-2 cursor-pointer"
                onClick={() => handleOptionToggle(option)}
              >
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-blue-600"
                  checked={selectedOptions.includes(option)}
                  readOnly
                />
                <span className="ml-2 text-gray-700">{option}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelectorDropdown;
