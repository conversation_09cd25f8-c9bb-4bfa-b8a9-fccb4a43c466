'use client'
import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import DesktopNavBar from "./DesktopNavBar/page";
import MobileNavBar from "./MobileNavBar/page";
import Search from "./Search/page";
import axios from "axios";
import { usePathname } from "next/navigation";

const navigation = [{ name: "Contact Us", href: "/contactus" }];

function CheckUserExitence(setUserExist){
  let myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");
  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    redirect: "follow",
  };
  fetch("/api/profile", requestOptions)
    .then((response) => response.json())
    .then((result) => {
      setUserExist(result.user)
    })
    .catch((error) => {
      console.log("error 27");
    });
}

export default function Header() {
  const pathname = usePathname();
  const [Sports, setSports] = useState([]);
  const [userExist, setUserExist] = useState(false); 

  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_BASE_URL}/api/category`);
        const sportsArray = response.data.data;
        if (Array.isArray(sportsArray)) {
          const sortedSports = sportsArray.sort((a, b) => a.name.localeCompare(b.name));
          setSports(sortedSports);
        } else {
          console.error("Error: Data is not in expected format");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
    CheckUserExitence(setUserExist);
  }, [pathname]);
  
  return (
    <header className="bg-gray-100">
      <nav
        className="mx-auto grid grid-cols-2 md:flex max-w-7xl items-center justify-between md:p-4 lg:px-8"
        aria-label="Global"
      >
        <div className="order-1 flex items-center justify-between p-4 md:p-0">
          <Link href="/" className="">
            <Image
              priority
                src="/MainKhelCoach.png"
              alt="HeaderLogo"
              width={100}
              height={100}
              className=" h-auto md:w-[152px] w-[104px]"
            />
          </Link>
        </div>
        <div className="md:bg-inherit	 bg-[#00AEEF] order-3 col-span-2 md:mt-0 md:order-2 p-4 md:p-0">
          <Search />
        </div>
        <div className="order-2 md:order-3 flex justify-end md:justify-normal p-4 md:p-0">
          <DesktopNavBar Sports={Sports} userExist={userExist} navigation={navigation} pathname={pathname}/>
          <MobileNavBar Sports={Sports} userExist={userExist} navigation={navigation} pathname={pathname}/>
        </div>
      </nav>
    </header>
  );
}
