.order-summary-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .order-summary-document {
    background: white;
    /* border: 2px solid rgb(4, 196, 225); */
    padding: 20px;
    width: 95%;
    max-width: 800px;
  }
  
  .billing-info-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .billing-info-left,
  .billing-info-right {
    width: 48%;
  }

  .close-button {
    text-align: right;
    width: 100%;
  }
  
  .diagonal-main {
    display: flex;
    height: 100px;
    margin-bottom: 20px;
  }
  
  .diagonal-left {
    width: 30%;
    background-color: rgb(242, 60, 60);
  }
  
  .diagonal-border {
    width: 18%;
    height: 100%;
    background: linear-gradient(
      to right top,
      rgb(242, 60, 60) 50%,
      rgb(13, 192, 242) 50%
    );
    margin: auto;
  }
  
  .diagonal-right {
    width: 60%;
    background-color: rgb(13, 192, 242);
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  
  .document-title {
    font-weight: bold;
    font-size: 24px;
    color: white;
    padding-right: 20px;
  }
  
  .summary-details {
    padding: 20px 0;
  }
  
  .summary-table table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .summary-table th, .summary-table td {
    border: 1px solid rgb(4, 196, 225);
    padding: 10px;
  }
  
  .total-label {
    font-weight: bold;
    color: red;
  }
  
  .booking-details {
    margin-top: 20px;
  }
  
  .booking-item {
    margin-bottom: 10px;
  }
  