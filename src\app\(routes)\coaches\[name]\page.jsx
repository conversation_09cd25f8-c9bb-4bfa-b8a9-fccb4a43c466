"use client";
import CoachInfoBlocks from "@/components/Coaches/CoachInfoBlocks/page";
import CoachDetails from "@/components/Coaches/CoachDetails/page";
import CoachCourseCards from "@/components/Coaches/CoachCourseCards/page";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
export default function Coach() {
  const [coachData, setCoachData] = useState("");
  const {name} = useParams();
  useEffect(() => {
    const myHeaders = new Headers();
    const requestOptions = {
      method: "GET",
      headers: myHeaders,
      redirect: "follow",
    };
    fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/coach/player/${name}`,
      requestOptions
    )
      .then((response) => response.json())
      .then((result) => setCoachData(result))
      .catch((error) => console.error(error));
  }, [name]);
  return (
    <div>
      <CoachDetails coachData={coachData} />
      <CoachInfoBlocks coachData={coachData} />
      <CoachCourseCards coachData={coachData} />
    </div>
  );
}
