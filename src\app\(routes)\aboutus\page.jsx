'use client';
import { useEffect, useState } from "react";
import axios from "axios";

export default function AboutUs() {
  const [aboutUsData, setAboutUsData] = useState(null);
  const [otherDetails, setOtherDetails] = useState([]);
  const [imageUrls, setImageUrls] = useState({}); // To store resolved image URLs

  useEffect(() => {
    const fetchAboutUsData = async () => {
      const requestOptions = {
        method: "GET",
        redirect: "follow",
      };

      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/cms/cms-about-us`, requestOptions);
        const result = await response.json();
        setAboutUsData(result[0]?.aboutUsData);
        setOtherDetails(result[0].founderDetails || []);
      } catch (error) {
        console.error("Error fetching About Us data:", error);
      }
    };

    fetchAboutUsData();
  }, []);

  // const extractPreviewUrl = async (url) => {
  //   try {
  //     const previewUrl = await axios.post(
  //       `${process.env.NEXT_PUBLIC_BASE_URL}/api/coach/download`,
  //       { location: url },
  //       {
  //         headers: {
  //           "Content-Type": "application/json",
  //         },
  //       }
  //     );
  //     console.log("-previewUrl.data.url", previewUrl.data.url)
  //     return previewUrl.data.url;
  //   } catch (error) {
  //     console.error("Error fetching preview URL:", error);
  //     return null;
  //   }
  // };

  // Fetch all images and store their resolved URLs
  // useEffect(() => {
  //   const fetchImageUrls = async () => {
  //     const urls = {};
  //     for (const detail of otherDetails) {
  //       if (detail.image) {
  //         const resolvedUrl = await extractPreviewUrl(detail.image);
  //         urls[detail.image] = resolvedUrl; // Map image location to resolved URL
  //       }
  //     }
  //     setImageUrls(urls);
  //   };

  //   if (otherDetails.length > 0) {
  //     fetchImageUrls();
  //   }
  // }, [otherDetails]);

  return (
    <>

      <div className="mx-auto max-w-7xl py-4 px-4 sm:py-6 sm:px-6 lg:py-8 lg:px-8">
        {/* Render About Us HTML content */}
        {aboutUsData && (
          <div dangerouslySetInnerHTML={{ __html: aboutUsData }} />
        )}
      </div>

      <div className="p-5">
        {otherDetails &&
          otherDetails.length > 0 &&
          otherDetails.map((x, index) => (
            <div key={x.id} className="flex flex-wrap items-center mb-4">
              {index % 2 === 0 ? (
                <>
                  <div className="w-full md:w-1/2 p-2">
                    {/* {imageUrls[x.image] ? ( */}
                    {x.image ? (
                      <img
                        className="h-64 w-64 rounded-lg"
                        // src={imageUrls[x.image]}
                        src={x.image}
                        alt="image description"
                      />
                    ) : (
                      <p>Loading image...</p>
                    )}
                  </div>
                  <div className="w-full md:w-1/2 p-2">
                    <div dangerouslySetInnerHTML={{ __html: x.description }} />
                  </div>
                </>
              ) : (
                <>
                  <div className="w-full md:w-1/2 p-2">
                    <div dangerouslySetInnerHTML={{ __html: x.description }} />
                  </div>
                  <div className="w-full md:w-1/2 p-2">
                    {imageUrls[x.image] ? (
                      <img
                        className="h-64 w-64  rounded-lg"
                        src={imageUrls[x.image]}
                        alt="image description"
                      />
                    ) : (
                      <p>Loading image...</p>
                    )}
                  </div>
                </>
              )}
            </div>
          ))}
      </div>

    </>
  );
}
