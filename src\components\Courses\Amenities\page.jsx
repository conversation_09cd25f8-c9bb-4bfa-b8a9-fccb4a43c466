export default function Amenities({data}) {
  return (
    <div className="enableLi mt-10 md:flex m-auto border py-8">
      <div className="md:w-1/2 m-auto md:m-0 md:border-r">
        <div className="w-fit m-auto">
          <button className="w-64 text-xl md:text-2xl text-white bg-sky-500 px-8 py-2 rounded-md mb-4 cursor-default">What I Have</button>
          <div className="w-56 text-sm" dangerouslySetInnerHTML={{ __html: data.amenitiesProvided }}/>
        </div>
      </div>
      <div className="md:w-1/2 m-auto md:m-0 mt-6 md:mt-0">
        <div className="w-fit m-auto">
          <button className="text-xl w-80 md:text-2xl text-white bg-sky-500 px-8 py-2 rounded-md mb-4 cursor-default">What you should bring</button>
{   data?.whatYouHaveToBring  ?      
  <div className="w-56 text-sm" dangerouslySetInnerHTML={{ __html: data.whatYouHaveToBring }}/>
  :  <div className="w-56 text-sm">Nothing to Bring</div>
}        </div>
      </div>
    </div>
  )
}