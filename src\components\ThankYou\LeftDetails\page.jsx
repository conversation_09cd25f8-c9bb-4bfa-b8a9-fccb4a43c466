export default function LeftDetails({ thankyouDetails }) {
    return (
        <div className="md:w-1/2 flex flex-col gap-5">

            <div className="bg-white border p-4 rounded">
                <p className="text-[17px] mb-6">Details</p>
                <div className="flex flex-col gap-4">
                    <div className="flex gap-2">
                        <p className="min-w-[100px]">Course Name :</p>
                        <p className="text-gray-400">{thankyouDetails?.courseId?.courseName}</p>
                    </div>
                    <div className="flex gap-2">
                        <p className="min-w-[100px]">Coach Name :</p>
                        <p className="text-gray-400">{thankyouDetails?.coachId?.firstName}{" "}{thankyouDetails?.coachId?.lastName}</p>
                    </div>
                    <div className="flex gap-2">
                        <p className="min-w-[100px]">Venue:</p>
                        <p className="text-gray-400">{thankyouDetails?.courseId?.facility?.name}{", "}{thankyouDetails?.courseId?.facility?.addressLine1}{", "}{thankyouDetails?.courseId?.facility?.addressLine2}{", "}{thankyouDetails?.courseId?.facility?.city}{", "}{thankyouDetails?.courseId?.facility?.state}{", "}{thankyouDetails?.courseId?.facility?.pinCode}</p>
                    </div>
                </div>
            </div>
            <div className="bg-white border text-sm p-4 rounded">
                <p className="text-[17px] mb-6">Payment Details</p>
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 justify-between w-full">
                        <div className="w-1/2">
                            <p className="">Payment Method:</p>
                            <p className="text-gray-400">{thankyouDetails?.paymentMode && thankyouDetails?.wallet ? "UPI & Wallet" : thankyouDetails?.paymentMode && !thankyouDetails?.wallet ? thankyouDetails?.paymentMode.toUpperCase() : "Wallet"}</p>
                        </div>
                        {thankyouDetails?.paymentId && (
                            <div className="w-1/2">
                                <p className="">Payment ID:</p>
                                <p className="text-gray-400">{thankyouDetails?.paymentId}</p>
                            </div>
                        )}

                    </div>
                    <div className="flex flex-col lg:flex-row lg:items-center gap-2  justify-between w-full">
                        <div className="w-1/2">
                            <p className="">Transaction id:</p>
                            <p className="text-gray-400">{thankyouDetails?.razorPayPaymentId ? thankyouDetails?.razorPayPaymentId?.toUpperCase() : thankyouDetails?.bookingId}</p>
                        </div>
                        {thankyouDetails?.wallet && (
                            <div className="w-1/2">
                                <p className="">Wallet Amount:</p>
                                <p className="text-gray-400">{thankyouDetails?.walletAmount?.toFixed(2)}</p>
                            </div>
                        )}

                    </div>

                </div>
            </div>
        </div>
    )
}