"use client";
import Link from "next/link";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { signIn, useSession } from "next-auth/react";

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
const signInSchema = Yup.object({
  email: Yup.string()
    .email("Please Enter a Valid Email")
    .matches(emailRegExp, "Please Enter a Valid Email")
    .required("Please enter your email"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters")
    .required("Please enter your password"),
});
const initialValues = {
  email: "",
  password: "",
};
export default function Login() {
  const session = useSession();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [userNotRegistered, setUserNotRegistered] = useState("");
  const [invalidCredentials, setInvalidCredentials] = useState("");
  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      validationSchema: signInSchema,
      onSubmit: (values, { resetForm }) => {
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        let requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: JSON.stringify(values),
          redirect: "follow",
        };
        fetch(`/api/login`, requestOptions)
          .then((response) => response.json())
          .then((result) => {
            setInvalidCredentials("");
            setUserNotRegistered("");
            if (
              result?.error?.message ==
              "Please try to login with correct credentials"
            ) {
              setInvalidCredentials(result.error.message);
            } else if (result?.error?.message == "User not registered") {
              setUserNotRegistered(result.error.message);
            } else if (result?.success) {
              setInvalidCredentials("");
              setUserNotRegistered("");
              resetForm();
              localStorage.getItem("Booked")
                ? router.push(`/courses/${localStorage.getItem("Booked")}`)
                : router.push("/");
            } else if (result?.error?.error == "Try to login with google") {
              setUserNotRegistered("Please use the Google button to login.");
            } else {
              console.log("check code 63");
            }
          })
          .catch((error) => {
            console.log("error 67");
          });
      },
    });
  useEffect(() => {
    if (session.status == "authenticated") {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      let emailObj = {
        email: session.data.user.email,
        firstName: session.data.user.name.split(" ")[0],
        lastName: session.data.user.name.split(" ")[1],
      };
      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(emailObj),
        redirect: "follow",
      };
      fetch(`/api/google`, requestOptions)
        .then((response) => response.json())
        .then((result) => {
          setInvalidCredentials("");
          setUserNotRegistered("");
          if (
            result?.error?.message ==
            "Please try to login with correct credentials"
          ) {
            setInvalidCredentials(result.error.message);
          } else if (result?.error?.message == "User not registered") {
            setUserNotRegistered(result.error.message);
          } else if (result?.success) {
            setInvalidCredentials("");
            setUserNotRegistered("");
            localStorage.getItem("Booked")
              ? router.push(`/courses/${localStorage.getItem("Booked")}`)
              : router.push("/");
          } else if (
            result?.error?.error == "Please login with email and password"
          ) {
            setUserNotRegistered(
              "Please try to login with email and password."
            );
          } else {
            console.log("check code 104");
          }
        })
        .catch((error) => {
          console.log("error 108");
        });
    } else if (session.status == "unauthenticated") {
      document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
      document.cookie =
        "player_id=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
    }
  }, [session.status]);
  return (
    <div className="w-full px-4 py-8 lg:py-16 sm:px-0 md:px-4">
      <div className="border py-4 sm:w-6/12 lg:w-4/12 2xl:w-3/12 shadow rounded-lg mx-auto">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <Image
            priority
            className="mx-auto h-15 md:w-[152px] w-[104px]"
              src="/MainKhelCoach.png"
            alt="Your Company"
            width={100}
            height={100}
          />
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            Sign in to your account
          </h2>
        </div>

        <div className="mt-10 mx-auto w-full max-w-xs sm:max-w-sm">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Email address
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  placeholder="Enter Email Password"
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6
                  ${
                    (errors.email && touched.email) || userNotRegistered
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                />
                {(errors.email && touched.email) || userNotRegistered ? (
                  <span className="form_errors form-email text-sm font-medium leading-6 text-red-500">
                    {userNotRegistered ? userNotRegistered : errors.email}
                  </span>
                ) : null}
              </div>
            </div>

            <div>
              <div className="flex items-center">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Password
                </label>
              </div>
              <div className="mt-2 relative">
                <input
                  placeholder="Enter Password"
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="off"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6 
                  ${
                    (errors.password && touched.password) || invalidCredentials
                      ? "border-2 border-rose-500"
                      : ""
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg
                      className="h-4 text-gray-700 cursor-pointer"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                    >
                      <path
                        fill="currentColor"
                        d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"
                      ></path>
                    </svg>
                  ) : (
                    <svg
                      className="h-4 text-gray-700 cursor-pointer"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                    >
                      <path
                        fill="currentColor"
                        d="M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z"
                      ></path>
                    </svg>
                  )}
                </button>
              </div>
              {(errors.password && touched.password) || invalidCredentials ? (
                <span className="form_errors form-password text-sm font-medium leading-6 text-red-500">
                  {invalidCredentials ? invalidCredentials : errors.password}
                </span>
              ) : null}
            </div>

            <div className="flex items-center justify-between">
              <p className="text-center text-sm text-gray-500">
                Not a member?
                <Link
                  href="/signup"
                  className="font-semibold leading-6 text-sky-500 hover:text-sky-400"
                >
                  {" "}
                  Sign Up
                </Link>
              </p>
              <div className="text-sm">
                <Link
                  href="/forget"
                  className="font-semibold text-sky-500 hover:text-sky-400"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400"
              >
                Sign in
              </button>
            </div>
            <div className="mt-10">
              <div className="relative">
                <div
                  className="absolute inset-0 flex items-center"
                  aria-hidden="true"
                >
                  <div className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center text-sm font-medium leading-6">
                  <span className="bg-white px-6 text-gray-900">
                    Or continue with
                  </span>
                </div>
              </div>
            </div>
          </form>

          <div className="mt-6">
            <button
              onClick={() => signIn("google", { prompt: "select_account" })}
              className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
            >
              <Image
                className="w-5"
                src="/google.svg"
                height={50}
                width={50}
                alt="googleSvg"
              />
              <span className="text-sm font-semibold leading-6">Google</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
