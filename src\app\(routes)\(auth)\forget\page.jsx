"use client";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ForgetPassword() {
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

  const [email, setEmail] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const router = useRouter();

  const handleForgetPassword = async (event) => {
    event.preventDefault();
    if (!emailRegExp.test(email)) {
      setErrorMessage("Please enter a valid email address.");
      return;
    }
    setErrorMessage("");

    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    let requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify({ email }), // Ensure email is sent as an object
      redirect: "follow",
    };

    try {
      const response = await fetch("/api/forget", requestOptions);
      const result = await response.json();
      if (result.error) {
        alert(result.error);
        return;
      }
      if (result.message) {
        alert(result.message);
        router.push("/");
      }
    } catch (error) {
      console.log("Error 37");
    }
  };

  return (
    <div className="w-full px-4 py-8 lg:py-16 sm:px-0 md:px-4">
      <div className="border py-4 lg:pb-10 sm:w-6/12 lg:w-4/12 2xl:w-3/12 shadow rounded-lg mx-auto">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <Image
            priority
            className="mx-auto h-15 md:w-[152px] w-[104px]"
            src="/MainKhelCoach.png"
            alt="Your Company"
            width={100}
            height={100}
          />
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
          <form className="space-y-6" onSubmit={handleForgetPassword}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Enter Email
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  onChange={(event) => {
                    setEmail(event.target.value);
                  }}
                  autoComplete="email"
                  placeholder="Enter email"
                  className={`p-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6`}
                />
              </div>
              {errorMessage && (
                <p className="mt-2 text-sm text-red-600">{errorMessage}</p>
              )}
            </div>

            <div className="lg:pt-2">
              <button
                type="submit"
                className="flex w-full justify-center rounded-md bg-red-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400"
              >
                Continue
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
