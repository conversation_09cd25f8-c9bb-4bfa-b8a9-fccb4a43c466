"use client";
import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { ExclamationCircleIcon } from "@heroicons/react/20/solid";
import ReactGoogleAutocomplete from "react-google-autocomplete";
import Link from "next/link";
import Image from "next/image";
import axios from "axios";

export default function Search() {
  const [searchInput, setSearchInput] = useState("");
  const [searchError, setSearchError] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searchLocationObj, setSearchLocationObj] = useState({});
  const [cityName, setCityName] = useState("");
  const [showDrop, setShowDrop] = useState("");
  const [coachImages, setCoachImages] = useState({});
  const [academyData, setAcademyData] = useState([]);
  const [activeTab, setActiveTab] = useState("courses");
  const router = useRouter();
  const modalRef = useRef(null);

  const getSportCategory = () => {
    if (
      searchInput.length < 3 &&
      !searchLocationObj.lat &&
      !searchLocationObj.lan
    ) {
      setSearchError(true);
    } else {
      setSearchError(false);
      let url =
        searchLocationObj.lat && searchLocationObj.lan
          ? `/categories/search?q=${searchInput}&lat=${searchLocationObj.lat}&long=${searchLocationObj.lan}`
          : `/categories/search?q=${searchInput}`;
      router.push(url);
    }
    setShowDrop("");
  };

  const getCurrentLocation = () => {
    try {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            setSearchLocationObj({
              lat: latitude,
              lan: longitude,
            });

            const geocoder = new window.google.maps.Geocoder();
            const latLng = new window.google.maps.LatLng(latitude, longitude);

            geocoder.geocode({ location: latLng }, (results, status) => {
              if (status === "OK" && results[0]) {
                const cityResult = results.find((result) =>
                  result.types.includes("locality")
                );
                if (cityResult) {
                  setCityName(cityResult.formatted_address);
                } else {
                  console.error("City not found in geocoding results.");
                }
              } else {
                console.error("Geocoding failed: " + status);
              }
            });
          },
          (error) => {
            console.error("Error getting user location:", error);
          }
        );
      } else {
        console.error("Geolocation is not supported by this browser.");
      }
    } catch (error) {
      console.log("error 82");
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const fetchCoachData = async (id) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/coach/player/${id}`
      );
      const data = response.data;
      return data?.profileImg;
    } catch (error) {
      console.error("Error fetching coach data:", error);
      return null;
    }
  };

  useEffect(() => {
    const fetchImages = async () => {
      const images = {};
      for (const result of searchResults) {
        if (!images[result.coach_id]) {
          const imageUrl = await fetchCoachData(result.coach_id);
          images[result.coach_id] = imageUrl;
        }
      }
      setCoachImages(images);
    };

    if (searchResults.length > 0) {
      fetchImages();
    }
  }, [searchResults]);

  const handleSearchApi = (value) => {
    setSearchInput(value);
    setSearchError(false);
    setShowDrop(value.length >= 3 ? value : "");
  };

  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      setShowDrop("");
    }
  };

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        let url =
          searchLocationObj.lat && searchLocationObj.lan
            ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/filter?lat=${searchLocationObj.lat}&long=${searchLocationObj.lan}&q=${searchInput}`
            : `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/filter?q=${searchInput}`;
        const response = await fetch(`${url}`, requestOptions);
        const result = await response.json();

        if (result.coursesAndCoaches) {
          setSearchResults(result.coursesAndCoaches);
        } else {
          setSearchResults(result);
        }

        if (result.academyData) {
          setAcademyData(result.academyData);
        }
      } catch (error) {
        console.log("error fetching courses");
      }
    };

    if (searchInput.length >= 3) {
      fetchCourses();
    }

    window.addEventListener("mousedown", handleClickOutside);

    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, [searchInput]);
  return (
    <div
      className="sm:flex justify-between rounded-lg md:space-x-2 w-full md:w-fit h-100 m-auto text-base "
      ref={modalRef}
    >
      <div>
        <ReactGoogleAutocomplete
          className="border md:p-2 p-4 rounded w-full md:w-auto"
          placeholder="Enter City Name"
          apiKey={process.env.NEXT_PUBLIC_GOOGLE_SEARCH}
          options={{ suppressDefaultStyles: true }}
          onChange={(place) => {
            if (!place.target.value) {
              setSearchLocationObj({ lat: null, lan: null });
            }
          }}
          onPlaceSelected={(place) => {
            setSearchLocationObj({
              lat: place?.geometry?.location.lat(),
              lan: place?.geometry?.location.lng(),
            });
          }}
          defaultValue={cityName}
        />
      </div>
      <div>
        <div className={searchError ? "relative rounded-md shadow-sm" : ""}>
          <input
            type="text"
            placeholder="Course/Coach/Sports"
            className={
              searchError
                ? "border mt-1 md:mt-0 md:p-2 p-4 rounded w-full md:w-auto text-red-900 ring-1 ring-inset ring-red-300 placeholder:text-red-300 focus:ring-2 focus:ring-inset focus:ring-red-500 sm:text-sm sm:leading-6"
                : "border mt-1 md:mt-0 md:p-2 p-4 rounded w-full md:w-auto"
            }
            value={searchInput}
            onChange={(e) => handleSearchApi(e.target.value)}
          />
          {showDrop && (searchResults.length > 0 || academyData?.length > 0) ? (
            <div
              className="flex flex-col gap-4 bg-white w-[90%] p-4 md:w-[75%] absolute left-[5%] md:left-[12%] z-10 top-[201] md:top-20 max-h-[35rem] h-auto md:max-h-[23rem] border rounded shadow-gray-950 overflow-auto"
              style={{ zIndex: 9999 }}
            >
              {/* Mobile Tabs */}
              <div className="flex md:hidden border-b mb-4">
                <button
                  className={`w-1/3 py-2 text-center rounded-t-md ${
                    activeTab === "courses"
                      ? "bg-gray-400 text-white font-medium"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("courses")}
                >
                  Courses
                </button>
                <button
                  className={`w-1/3 py-2 text-center rounded-t-md ${
                    activeTab === "coaches"
                      ? "bg-gray-400 text-white font-medium"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("coaches")}
                >
                  Coaches
                </button>
                <button
                  className={`w-1/3 py-2 text-center rounded-t-md ${
                    activeTab === "academies"
                      ? "bg-gray-400 text-white font-medium"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("academies")}
                >
                  Academies
                </button>
              </div>

              <div className="flex flex-col md:flex-row gap-[20px]">
                {/* Courses Section */}
                <div
                  className={`md:w-1/3 w-full flex flex-col ${
                    activeTab === "courses" || activeTab === ""
                      ? "block"
                      : "hidden md:block"
                  }`}
                >
                  <p className="hidden md:block mb-4 pl-[10px] text-slate-400">
                    Courses
                  </p>
                  {searchResults && Array.isArray(searchResults) && searchResults.filter(x => x.course !== "false" && x.courseName).length > 0 && (
                    searchResults
                      .filter(x => x.course !== "false" && x.courseName)
                      .map((x, index) => (
                        <Link
                          key={index}
                          href={`/courses/${x._id}`}
                          onClick={() => setShowDrop("")}
                          className="rounded flex flex-row gap-4 hover:bg-slate-200 w-full px-[10px] py-[10px] md:border-b md:border-slate-200 "
                        >
                          <Image
                            src={x?.images?.[0]?.url || "/placeholder-image.jpg"}
                            alt="CourseImage"
                            width={100}
                            height={100}
                            className="flex-none rounded object-cover h-14 w-14"
                          />
                          <div className="flex flex-col gap-2">
                            <h3 className="pr-10 font-semibold text-sm text-gray-900 xl:pr-0 line-clamp-2">
                              {x?.courseName}
                            </h3>
                            <h3 className="pr-10 font-semibold text-sm text-sky-500 xl:pr-0">
                              {`${
                                x?.fees?.feesCourse
                                  ? `₹ ${x?.fees?.feesCourse}`
                                  : x?.fees?.fees
                                  ? `starts from ₹${x?.fees?.fees}`
                                  : ""
                              }`}
                            </h3>
                          </div>
                        </Link>
                      ))
                  )}
                </div>

                {/* Coaches Section */}
                <div
                  className={`md:w-1/3 w-full flex flex-col ${activeTab === 'coaches' ? 'block' : 'hidden md:block'}`}
                >
                  <p className="hidden md:block mb-4 pl-[10px] text-slate-400">Coaches</p>
                  {searchResults && Array.isArray(searchResults) && searchResults.filter(x => x.coach_id).length > 0 && (
                    searchResults
                      .filter(x => x.coach_id)
                      .map((x, index) => {
                        const prevCoachNames = searchResults
                          .slice(0, searchResults.indexOf(x))
                          .map((prevItem) => prevItem.coach_id);

                        if (prevCoachNames.includes(x.coach_id)) {
                          return null;
                        }

                        return (
                          <Link
                            key={index}
                            href={`/coaches/${x.coach_id}`}
                            onClick={() => setShowDrop("")}
                            className="rounded flex flex-row gap-4 hover:bg-slate-200 w-full px-[10px] py-[10px] border-b border-slate-200 "
                          >
                            <Image
                              src={
                                coachImages[x.coach_id] ||
                                "/placeholder-image.jpg"
                              }
                              alt="CoachImage"
                              width={100}
                              height={100}
                              className="flex-none rounded object-cover h-14 w-14"
                            />
                            <div className="flex flex-col justify-between">
                              <h3 className="font-semibold text-sm text-gray-900 line-clamp-2">
                                {x?.coachName}
                              </h3>
                            </div>
                          </Link>
                        );
                      })
                  )}
                </div>

                {/* Academies Section */}
                <div
                  className={`md:w-1/3 w-full flex flex-col ${
                    activeTab === "academies" ? "block" : "hidden md:block"
                  }`}
                >
                  <p className="hidden md:block mb-4 pl-[10px] text-slate-400">
                    Academies
                  </p>
                  {academyData && academyData.length > 0 ? (
                    academyData.map((academy, index) => (
                      <Link
                        key={index}
                        href={`/academies/${academy._id}`}
                        onClick={() => setShowDrop("")}
                        className="rounded flex flex-row gap-4 hover:bg-slate-200 w-full px-[10px] py-[10px] border-b border-slate-200"
                      >
                        <Image
                          src={academy.profileImage || "/placeholder-image.jpg"}
                          alt="AcademyImage"
                          width={100}
                          height={100}
                          className="flex-none rounded object-cover h-14 w-14"
                        />
                        <div className="flex flex-col justify-between">
                          <h3 className="font-semibold text-sm text-gray-900 line-clamp-2">
                            {academy.academyName}
                          </h3>
                        </div>
                      </Link>
                    ))
                  ) : (
                    <p className="pl-[10px] text-gray-500">
                      No academies found
                    </p>
                  )}
                </div>
              </div>
            </div>
          ) : (
            showDrop && (
              <div className="flex flex-col gap-4 bg-white p-4 absolute z-10 top-60 md:top-20 border rounded shadow-gray-950 overflow-auto">
                <p>No Search Results Found</p>
              </div>
            )
          )}

          {searchError && (
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <ExclamationCircleIcon
                className="h-5 w-5 text-red-500"
                aria-hidden="true"
              />
            </div>
          )}
        </div>
      </div>
      <div>
        <button
          className="text-base font-semibold leading-6 px-6 mt-1 md:mt-0 md:py-2 p-4 bg-black text-white rounded-md w-full md:w-auto"
          onClick={getSportCategory}
        >
          Search
        </button>
      </div>
    </div>
  );
}
