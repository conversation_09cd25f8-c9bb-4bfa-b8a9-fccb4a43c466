import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function TopAcademies({ data, blockData }) {
  const [homeAcademies, setHomeAcademies] = useState([]);

  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeAcademies(sortedData);
  }, [data]);

  return (
    <div>
      {homeAcademies.length > 0 && (
        <div className="bg-white">
          <div className="mx-auto max-w-2xl px-4 md:py-10 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
            <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-6 uppercase">
              {blockData?.title}
            </h2>

            <Carousel
              additionalTransfrom={0}
              arrows
              autoPlaySpeed={3000}
              centerMode={false}
              containerClass="container desktopView"
              draggable
              focusOnSelect={false}
              infinite={true}
              itemClass="carousel-item-padding-40-px"
              keyBoardControl
              minimumTouchDrag={80}
              renderButtonGroupOutside={true}
              renderDotsOutside={false}
              responsive={{
                desktop: {
                  breakpoint: {
                    max: 3000,
                    min: 1024,
                  },
                  items: 4,
                  partialVisibilityGutter: 40,
                },
                mobile: {
                  breakpoint: {
                    max: 464,
                    min: 0,
                  },
                  items: 1.5,
                  infinite: false,
                  partialVisibilityGutter: 40,
                },
                tablet: {
                  breakpoint: {
                    max: 1024,
                    min: 464,
                    infinite: false,
                  },
                  items: 3,
                  partialVisibilityGutter: 40,
                },
              }}
              showDots={false}
              sliderClass=""
              slidesToSlide={1}
              swipeable
            >
              {homeAcademies &&
                homeAcademies.map((academyItem, index) =>
                  academyItem?.academy ? (
                    <div key={index} className="flex-none">
                      <div className="w-full rounded-2xl object-cover">
                        <Link href={`/academies/${academyItem?.academy?._id}`}>
                          <Image
                            src={
                              academyItem?.academy?.profileImage ||
                              "/MainKhelCoach.png"
                            }
                            alt={academyItem?.academy?.name}
                            width={500}
                            height={500}
                            className="w-100 h-52 rounded-t-lg object-cover"
                          />
                        </Link>
                      </div>
                      <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                        <div className="mt-4 flex flex-col md:gap-4 gap-2">
                          <h3>
                            <Link
                              className="text-[14px] md:text-lg not-italic font-medium text-black h-[26px] line-clamp-1"
                              href={`/academies/${academyItem?.academy?._id}`}
                            >
                              <span aria-hidden="true" />
                              {academyItem?.academy?.name || "Academy Name"}
                            </Link>
                          </h3>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center underline gap-2">
                              <Link
                                className="text-[14px] md:text[16px]"
                                href={`/academies/${academyItem?.academy?._id}`}
                              >
                                View Academy
                              </Link>
                              <Image
                                src="/Arrow.svg"
                                alt="arrowsvg"
                                width={20}
                                height={20}
                                className="lg:w-3 2xl:w-4 h-auto object-cover"
                                style={{ width: "auto", height: "auto" }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : null
                )}
            </Carousel>
          </div>
        </div>
      )}
    </div>
  );
} 