import { useState, useEffect } from "react";
import Image from "next/image";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function AcademyImagesCarousel({ images }) {
  const [academyImages, setAcademyImages] = useState([]);
  
  useEffect(() => {
    if (images && images.length > 0) {
      setAcademyImages(images);
    }
  }, [images]);

  if (!academyImages || academyImages.length === 0) {
    return null;
  }

  return (
    <div>
      <style jsx>{`
        .academy-card {
          width: 558px !important;
          height: 395px !important;
          min-width: 558px !important;
          max-width: 558px !important;
        }
        
        @media (max-width: 768px) {
          .academy-card {
            width: 558px !important;
            height: 395px !important;
            min-width: 558px !important;
            max-width: 558px !important;
          }
        }

        .carousel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .carousel-title {
          font-size: 1.5rem;
          font-weight: bold;
          text-transform: uppercase;
          color: white;
        }

        @media (max-width: 768px) {
          .carousel-title {
            display: none;
          }
        }
      `}</style>
      <div className="academyImagesSec pb-3">
        <div className="mx-auto max-w-7xl">
          <div className="carousel-header">
            <h2 className="carousel-title">Gallery</h2>
          </div>
          <Carousel
            additionalTransfrom={0}
            arrows
            autoPlaySpeed={3000}
            centerMode={false}
            autoPlay={false}
            containerClass="container"
            draggable={false}
            focusOnSelect={false}
            infinite={true}
            itemClass="carousel-item-padding-40-px"
            keyBoardControl
            minimumTouchDrag={80}
            renderButtonGroupOutside={false}
            renderDotsOutside={false}
            responsive={{
              desktop: {
                breakpoint: {
                  max: 3000,
                  min: 1024,
                },
                items: 3,
                partialVisibilityGutter: 40,
              },
              mobile: {
                breakpoint: {
                  max: 464,
                  min: 0,
                },
                items: 1,
                partialVisibilityGutter: 30,
              },
              tablet: {
                breakpoint: {
                  max: 1024,
                  min: 464,
                },
                items: 2,
                partialVisibilityGutter: 100,
              },
            }}
            showDots={false}
            sliderClass=""
            slidesToSlide={1}
            swipeable
          >
            {academyImages?.map((imageUrl, index) => (
              <div
                key={index}
                className="flex-none lg:flex-auto max-w-xl flex-col items-start justify-between bg-white p-3 pt-0 rounded-[6px] w-full"
              >
                <div className="group relative w-full h-64 md:h-80 overflow-hidden rounded-[6px]">
                  <Image
                    src={imageUrl}
                    alt={`Academy image ${index + 1}`}
                    fill
                    className="object-contain transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 60vw, 40vw"
                  />
                </div>
              </div>
            ))}
          </Carousel>
        </div>
      </div>
    </div>
  );
}