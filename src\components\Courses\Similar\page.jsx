import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import axios from "axios";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function RecommendedCourses() {
  const [Courses, setCourses] = useState([]);
  let search = localStorage.getItem("filter");
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/filter?${search}`
        );
        setCourses(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, [search]);
  return (
    <div className="bg-white">
      {Courses.length > 0 && (
        <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 lg:max-w-7xl lg:px-0">
          <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-4 md:mb-6 uppercase">
            Similar Courses
          </h2>
          <Carousel
            additionalTransfrom={0}
            arrows
            autoPlaySpeed={3000}
            centerMode={false}
            containerClass="container"
            draggable
            focusOnSelect={false}
            infinite
            itemClass="carousel-item-padding-40-px"
            keyBoardControl
            minimumTouchDrag={80}
            renderButtonGroupOutside={false}
            renderDotsOutside={false}
            responsive={{
              desktop: {
                breakpoint: {
                  max: 3000,
                  min: 1024,
                },
                items: 4,
                partialVisibilityGutter: 40,
              },
              mobile: {
                breakpoint: {
                  max: 464,
                  min: 0,
                },
                items: 2,
                partialVisibilityGutter: 30,
              },
              tablet: {
                breakpoint: {
                  max: 1024,
                  min: 464,
                },
                items: 3,
                partialVisibilityGutter: 30,
              },
            }}
            showDots={false}
            sliderClass=""
            slidesToSlide={1}
            swipeable
          >
            {Courses.map((course, index) => (
              <div
                key={index}
                className="flex-none"
              >
                <div className="aspect-[3/2] w-full rounded-2xl object-cover">
                  <Image
                    src={course?.images[0]?.url}
                    alt={course.courseName}
                    width={500}
                    height={500}
                    className="w-100 h-52 rounded-t-lg object-contain"
                  />
                </div>
                <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                  <div className="mt-4 flex justify-between items-center text-base not-italic font-medium">
                    <Link href={`/courses/${course._id}`}>
                      <p className="text-white px-3.5 py-2 bg-red-600 rounded-lg">
                        {course && course?.fees?.feesCourse
                          ? `₹ ${course.fees.feesCourse}`
                          : "Explore"}
                      </p>
                    </Link>
                    {/* <p className="text-sky-500">3 Classes/Week</p> */}
                  </div>
                  <div>
                    <h3 className="text-lg not-italic font-medium text-black h-[53px]">
                      <Link href={`/courses/${course._id}`}>
                        <span aria-hidden="true" className="absolute inset-0" />
                        {course.courseName}
                      </Link>
                    </h3>
                    <p
                      className="mt-1 font-light text-gray-400 not-italic line-clamp-2 h-[53px]"
                      dangerouslySetInnerHTML={{ __html: course.description }}
                    ></p>

                    <div className="mt-4 flex items-center underline gap-2">
                      <Link href={`/courses/${course._id}`}>
                        What will you learn{" "}
                      </Link>
                      <Image
                        src="/Arrow.svg"
                        alt="arrowsvg"
                        width={20}
                        height={20}
                        className="w-4 h-auto"
                        style={{ width: "auto", height: "auto" }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Carousel>
        </div>
      )}

    </div>
  );
}
