import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function CoachCourseCards({ coachData }) {
  const [coachCourses, setCoachCourses] = useState([]);

  const [activeCourses, setActiveCourses] = useState([]);

  const params = useParams();
  useEffect(() => {
    const requestOptions = {
      method: "GET",
      redirect: "follow",
    };

    fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/coach/${params.name}`,
      requestOptions
    )
      .then((response) => response.json())
      .then((result) => setCoachCourses(result))
      .catch((error) => console.error(error));
  }, [params.name]);

  useEffect(() => {
    const filteredActiveCourses = coachCourses?.data?.filter(
      (course) => course.status === "active"
    );
    setActiveCourses(filteredActiveCourses);
  }, [coachCourses]);
  return activeCourses?.length > 0 ? (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 md:py-16 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
        <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-4 md:mb-6 uppercase">
          {`MORE BY ${coachData?.firstName?.toUpperCase()} ${coachData?.lastName?.toUpperCase()}`}
        </h2>

        <Carousel
          additionalTransfrom={0}
          arrows
          autoPlaySpeed={3000}
          centerMode={false}
          containerClass="container desktopView"
          draggable
          focusOnSelect={false}
          infinite={true}
          itemClass="carousel-item-padding-40-px"
          keyBoardControl
          minimumTouchDrag={80}
          renderButtonGroupOutside={true}
          renderDotsOutside={false}
          responsive={{
            desktop: {
              breakpoint: {
                max: 3000,
                min: 1024,
              },
              items: 4,
              partialVisibilityGutter: 40, // Adjust this value to add space between items
            },
            mobile: {
              breakpoint: {
                max: 464,
                min: 0,
              },
              items: 1.5,
              partialVisibilityGutter: 40, // Adjust this value to add space between items
            },
            tablet: {
              breakpoint: {
                max: 1024,
                min: 464,
              },
              items: 3,
              partialVisibilityGutter: 40, // Adjust this value to add space between items
            },
          }}
          showDots={false}
          sliderClass=""
          slidesToSlide={1}
          swipeable
        >
          {activeCourses?.map((product, index) => (
            <div key={index} className="flex-none">
              <div className="relative w-full overflow-hidden rounded-lg bg-white aspect-h-1 sm:aspect-h-1 sm:aspect-w-2 lg:aspect-h-1 lg:aspect-w-1 group-hover:opacity-75 h-34 sm:h-36 md:h-48">
                <Link href={`/courses/${product._id}`}>
                  <Image
                    src={product?.images[0]?.url}
                    alt="/skate.png"
                    className="h-full w-full object-cover object-center"
                    width={500}
                    height={500}
                  />
                </Link>
              </div>
              <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                <div className="mt-4 flex justify-between items-center text-base not-italic font-medium">
                  <Link href={`/courses/${product._id}`}>
                    <p className="text-white px-3.5 py-2 bg-red-600 rounded-lg">
                      {`${
                        product.fees && product.fees.feesCourse
                          ? "₹ " + product.fees.feesCourse
                          : "Explore"
                      }`}
                    </p>
                  </Link>
                </div>
                <div className="flex flex-col gap-4">
                  <h3>
                    <Link
                      className="text-[16px] md:text-lg not-italic font-medium text-black h-[26px] line-clamp-1"
                      href={`/courses/${product._id}`}
                    >
                      <span aria-hidden="true" />
                      {product.courseName}
                    </Link>
                  </h3>
                  <p
                    className="text-[14px] md:text-[16px] font-light text-gray-400 not-italic line-clamp-2"
                    dangerouslySetInnerHTML={{ __html: product.description }}
                  />
                  <div className="flex items-center underline gap-2">
                    <Link
                      className="text-[14px] md:text-[16px]"
                      href={`/courses/${product._id}`}
                    >
                      What will you learn{" "}
                    </Link>
                    <Image
                      src="/Arrow.svg"
                      alt="arrowsvg"
                      width={20}
                      height={20}
                      className="w-4 h-auto"
                      style={{ width: "auto", height: "auto" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  ) : (
    ""
  );
}
