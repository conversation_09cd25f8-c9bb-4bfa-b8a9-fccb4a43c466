import React from "react";
import Image from "next/image";

const Days = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];
export default function Timings({
  data,
  handleBookings,
  bookingArray,
  setBookingArray,
  isLoggedIn,
}) {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { day: "numeric", month: "short", year: "numeric", timeZone: "UTC" };
    return date
      .toLocaleDateString("en-GB", options)
      .replace(/(\d+)(th|nd|rd|st)/, "$1$2");
  };


  const formattedStartDate = formatDate(data.dates.startDate);
  const formattedEndDate = formatDate(data.dates.endDate);

  const formatTime = (timeString) => {
    const [hour, minute] = timeString.split(":");
    const meridiem = parseInt(hour) >= 12 ? " pm" : " am";
    const formattedHour = parseInt(hour) % 12 || 12;
    return `${formattedHour}:${minute}${meridiem}`;
  };
  function convertTo12HourFormat(timeString) {
    let times = timeString.split(" to ");
    let convertedTimes = times.map(time => {
      let [hours, minutes] = time.split(":");
      hours = parseInt(hours);
      minutes = parseInt(minutes);

      let period = hours >= 12 ? "PM" : "AM";
      if (hours > 12) {
        hours -= 12;
      } else if (hours === 0) {
        hours = 12;
      }

      return `${hours}:${minutes < 10 ? '0' : ''}${minutes} ${period}`;
    });
    return convertedTimes.join(" to ");
  }

  const formattedStartTime = formatTime(data.dates.startTime);
  const formattedEndTime = formatTime(data.dates.endTime);
  const handleDeleteSession = (detail) => {
    const updatedBookingArray = bookingArray.filter(
      (session) => session.date !== detail.date || session.time !== detail.time
    );
    setBookingArray(updatedBookingArray);
  };

  return (
    <div className="border text-base p-6 m-auto md:m-0 rounded-lg w-full">
      <div className="flex justify-between">
        <p>Slot details</p>
        <p>{data.classType === 'course' ? `Group Size: ${data.playerEnrolled}/${data.maxGroupSize}` : null}</p>
      </div>

      {data.classType === "class" ? (
        bookingArray.length ? (
          <>
            <div className="overflow-y-auto max-h-[20vh]">
              {bookingArray.map((detail, index) => (
                <div key={index} className="border-b mt-4 pb-2 flex items-center justify-between max-sm:text-sm text-xs ">
                  <span className="w-[20%] max-sm:w-auto">
                    {new Date(detail.date)
                      .toLocaleDateString("en-GB", {
                        day: "numeric",
                        month: "long",
                        year: "2-digit",
                      })
                      .replace(/(\d+)(th|nd|st|rd)/, "$1<sup>$2</sup>")}
                  </span>
                  <p className="text-gray-700">
                    {navigator.platform === "MacIntel"
                      ? convertTo12HourFormat(detail.time)
                      : detail.time}
                  </p>
                  <div className="flex gap-2">
                    <p className="flex whitespace-nowrap">{`₹ ${parseInt(detail?.price.split(" ")[1])}`}</p>
                    <div className="flex justify-end">
                      <Image
                        onClick={() => handleDeleteSession(detail)}
                        src="/Binicon.svg"
                        alt="Binicon"
                        height={50}
                        width={50}
                        className="w-4 cursor-pointer"
                      />
                    </div>
                  </div>

                </div>
              ))}
            </div>
            <div className="mt-4 flex justify-end">
              <button
                className="bg-black text-white px-4 py-2 rounded-lg"
                onClick={handleBookings}
              >
                Add
              </button>
            </div>
          </>
        ) : (
          <button
            className="mt-4 text-base cursor-pointer bg-gray-100 border w-full shadow-sm p-4 rounded-lg"
            onClick={handleBookings}
          >
            {isLoggedIn ? "Select Slots to Book" : "Login To Book Slots"}
          </button>
        )
      ) : (
        <div className="flex flex-col gap-2">
          {formattedStartDate} - {formattedEndDate}
          <div className="flex items-center justify-between">
            {Days.map((day, index) => (
              <div
                key={index}
                className={`rounded-full p-1 w-[10%] flex justify-center items-center text-xs ${data.dates.days.filter((x) => x.startsWith(day)).length
                  ? "bg-sky-300"
                  : "bg-gray-200"
                  }`}
              >
                {day}
              </div>
            ))}
          </div>
          <div>
            <h3>Time</h3>
            <div>
              {formattedStartTime} - {formattedEndTime}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
