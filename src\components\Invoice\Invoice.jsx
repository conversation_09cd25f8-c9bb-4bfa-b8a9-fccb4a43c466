import React from "react";
import { useEffect, useState, useRef } from "react";
import "./Invoice.css";
import { useSearchParams } from "next/navigation";
import { State } from "country-state-city";
const Invoice = ({ bookingIds }) => {
  const params = useSearchParams();
  const [thankyouDetails, setThankyouDetails] = useState();
  const [booking_id, setBooking_Id] = useState(
    bookingIds || params.get("booking_id")
  );
  const [playerHomeState, setPlayerHomeState] = useState("");
  const [registeredState, setRegisteredState] = useState("Delhi");
  const countryCode = 'IN';
  useEffect(() => {
    if (bookingIds) {
      setBooking_Id(bookingIds);
    }
  }, [bookingIds]);

  useEffect(() => {
    if (booking_id) {
      getUserToken(booking_id);
    }
  }, [booking_id]);

  async function getUserToken(booking_id) {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result = await response.json();
    if (result?.user?.token) {
      getBookingById(result.user.token, booking_id);
    }
  }
  async function getBookingById(token, booking_id) {
    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${booking_id}`,
      requestOptions
    )
      .then((response) => response.json())
      .then((result) => {
        setThankyouDetails(result);

        // Convert GST state ISO code to full state name
        const homeState = State.getStateByCodeAndCountry(
          result?.player?.homeState,
          "IN"
        )?.name || "";

        setPlayerHomeState(homeState);
      })
      .catch((error) => console.error(error));
  }
  // amount breakdown
  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const platformFee = thankyouDetails?.academyId?.platformFee
    ? thankyouDetails.academyId.platformFee / 100
    : 0.12;

  const platformTax = subtotal * platformFee;

  // const platformTax = subtotal * 0.12;
  const taxGST = platformTax * 0.18;

  const isSameState = playerHomeState === registeredState;
  const cgst = isSameState ? taxGST / 2 : 0;
  const sgst = isSameState ? taxGST / 2 : 0;
  const igst = taxGST;
  let from =
    "Umn Khel Shiksha Private Limited,Vasant Vihar,Basant Lok Complex,Road 21,New Delhi-110057";
  let billto = "Dummy title,Dummy address ,Dummy address,Dummy-XXXXX";
  let invoiceno = thankyouDetails?.bookingId;
  let date = new Date(thankyouDetails?.createdAt).toLocaleDateString("en-IN", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });
  let total_amount = thankyouDetails?.pricePaid;
  let gstid = "07AADCU2822L1Z8";

  return (
    <div>
      {/* MAIN BOX */}
      <div>
        {/* for heading  */}
        <div className="diagonal_main">
          <div className="diagonal_left"></div>
          <div class="diagonal-border"></div>
          <div class="diagonal_right">
            <p class="font-bold text-white text-2xl pt-32 text-end pr-32">
              INVOICE
            </p>
          </div>
        </div>
        {/* FROM AND INVOICE NO BOX  */}
        <div class="flex justify-between pl-6 pr-6">
          <div>
            <div class="flex gap-1.5">
              <p className="font-semibold">From :</p>
              <div class=" flex flex-col text-left">
                {from.split(",").map((item, index) => (
                  <p key={index}>{item}</p>
                ))}
              </div>
            </div>
            <p className="mt-4">GST ID : {gstid}</p>
          </div>
          <div class="flex gap-6 ">
            <div class="flex flex-col text-left">
              <p class="font-bold">Invoice No :</p>
              <p class="font-bold">Date :</p>
              {/* <p class="font-bold">Total Amount :</p> */}
            </div>
            <div class="flex flex-col text-left">
              <p>{invoiceno}</p>
              <p>{date}</p>
              {/* <p>₹{total_amount}</p> */}
            </div>
          </div>
        </div>
        {/* BILL TO BOX */}
        <div class="flex justify-between pl-6 pr-6 mb-20">
          <div>
            <div class="flex gap-1.5 mt-4">
              <p class="font-semibold">Bill :</p>
              <div class="flex flex-col text-left">
                <p>{thankyouDetails?.playerName}</p>
                <p>{thankyouDetails?.player?.mobile}</p>
                <p>{thankyouDetails?.playerEmail}</p>
              </div>
            </div>
          </div>
        </div>
        {/* DESCRIPTIO AND AMOUNT BOX */}
        <div style={{ marginTop: "2%" }}>
          <table>
            <tr style={{ color: "red" }}>
              <th>DESCRIPTION/MEMO</th>
              <th>TOTAL AMOUNT</th>
            </tr>
            <tr style={{ verticalAlign: "top" }}>
              <td>Service Fee</td>
              <td>₹{platformTax.toFixed(2)}</td>
            </tr>
            {isSameState && (
              <>
                <tr>
                  <td>CGST(9%)</td>
                  <td>₹{cgst?.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>SGST(9%)</td>
                  <td>₹{sgst?.toFixed(2)}</td>
                </tr>
              </>
            )}
            {!isSameState && (
              <tr>
                <td>IGST(18%)</td>
                <td>₹{igst?.toFixed(2)}</td>
              </tr>
            )}
            <tr>
              <td style={{ color: "red", fontWeight: "bold" }}>TOTAL AMOUNT</td>
              <td>₹{Math.ceil(platformTax + platformTax * 0.18).toFixed(2)}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  );
};
export default Invoice;
