import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";
import { <PERSON>ag<PERSON><PERSON> } from "next/font/google";

export default function RecommendedCoaches({ data, blockData }) {
  const [homeCoach, setHomeCoach] = useState([]);
  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeCoach(sortedData);
  }, [data]);
  return (
    <div>
      {homeCoach.length > 0 && (
        <div className="bg-white">
          <div className="mx-auto max-w-2xl px-4 md:py-6 py-4 sm:px-6 lg:max-w-7xl lg:px-8">
            <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-6 uppercase">
              {blockData?.title}
            </h2>

            <Carousel
              additionalTransfrom={0}
              arrows
              autoPlaySpeed={3000}
              centerMode={false}
              containerClass="container desktopView"
              draggable
              focusOnSelect={false}
              infinite={true}
              itemClass="carousel-item-padding-40-px"
              keyBoardControl
              minimumTouchDrag={80}
              renderButtonGroupOutside={true}
              renderDotsOutside={false}
              responsive={{
                desktop: {
                  breakpoint: {
                    max: 3000,
                    min: 1024,
                  },
                  items: 4,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
                mobile: {
                  breakpoint: {
                    max: 464,
                    min: 0,
                  },
                  items: 1.5,
                  infinite: false,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
                tablet: {
                  breakpoint: {
                    max: 1024,
                    min: 464,
                    infinite: false,
                  },
                  items: 3,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
              }}
              showDots={false}
              sliderClass=""
              slidesToSlide={1}
              swipeable
            >
              {homeCoach &&
                homeCoach.map((product, index) =>
                  product?.coach ? (
                    <div key={index} className="flex-none">
                      <div className="w-full rounded-2xl object-cover">
                        <Link href={`/coaches/${product?.coach?._id}`}>
                          {product?.coach?.profileImg ? (
                            <Image
                              src={product?.coach?.profileImg}
                              alt={product?.coach?.firstName}
                              width={500}
                              height={500}
                              className="w-100 h-52 rounded-t-lg object-cover"
                            />
                          ) : (
                            <div className="w-100 h-52 rounded-t-lg bg-gray-200 flex items-center justify-center">
                              <div className="text-center text-gray-500">
                                <div className="text-sm">No Image</div>
                              </div>
                            </div>
                          )}
                        </Link>
                      </div>
                      <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                        <div className="mt-4 flex flex-col md:gap-4 gap-2">
                          <h3>
                            <Link
                              className="text-[14px] md:text-lg not-italic font-medium text-black h-[26px] line-clamp-1"
                              href={`/coaches/${product?.coach?._id}`}
                            >
                              <span aria-hidden="true" />
                              {`${product?.coach?.firstName} ${product?.coach?.lastName}`}
                            </Link>
                          </h3>
                          {product?.coach?.sportsCategories && product?.coach?.sportsCategories.length > 0 && (
                            <h3 className="font-light text-gray-600 not-italic">
                              {product?.coach?.sportsCategories.join(', ')}
                            </h3>
                          )}
                          <p className="text-[14px] md:text[16px] font-light text-gray-400 not-italic line-clamp-2">
                            {product?.coach?.firstName} is a dedicated coach at our academy, committed to helping athletes achieve their goals and improve their performance.
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center underline gap-2">
                              <Link
                                className="text-[14px] md:text[16px]"
                                href={`/coaches/${product?.coach?._id}`}
                              >
                                View Profile
                              </Link>
                              <Image
                                src="/Arrow.svg"
                                alt="arrowsvg"
                                width={20}
                                height={20}
                                className="lg:w-3 2xl:w-4 h-auto object-cover"
                                style={{ width: "auto", height: "auto" }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : null
                )}
            </Carousel>
          </div>
        </div>
      )}
    </div>
  );
}
