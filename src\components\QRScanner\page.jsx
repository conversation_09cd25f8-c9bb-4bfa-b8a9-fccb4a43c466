import { useState } from 'react';
import { Scanner } from '@yudiel/react-qr-scanner';

export default function QRScanner({ playerObject, setShowScanner }) {
    const [loading, setLoading] = useState(false);
    const [attendanceSuccess, setAttendanceSuccess] = useState(false);
    const [attendanceError, setAttendanceError] = useState();
    const [isVisible, setIsVisible] = useState(true)

    const handleAttendance = async (data) => {
        setLoading(true);
        setIsVisible(false);
        try {
            if (!attendanceSuccess) {
                let requestOptions = {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    redirect: "follow",
                };
                const response = await fetch("/api/profile", requestOptions);
                const result = await response.json();
                if (result?.user?.token) {
                    const myHeaders = new Headers();
                    myHeaders.append("Content-Type", "application/json");
                    myHeaders.append("Authorization", `Bearer ${result?.user?.token}`);
                    let raw = {
                        player: playerObject?._id,
                    };
                    data?.maxGroupSize === 1 ? raw.Class = data?.classId : raw.courseId = data?.courseId;
                    const requestOptions = {
                        method: "POST",
                        headers: myHeaders,
                        body: JSON.stringify(raw),
                        redirect: "follow"
                    };
                    let url = data?.maxGroupSize === 1 ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/attendance/${data?.bookingId}` : `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/markAttendance`;
                    const response1 = await fetch(url, requestOptions);
                    const result1 = await response1.json();
                    if (result1) setLoading(false);
                    setIsVisible(false);
                    if (result1?.error) {
                        setAttendanceError(result1?.error)
                        setAttendanceSuccess(false)
                    } else {
                        setAttendanceError(false);
                        setAttendanceSuccess(true);
                    }
                    setTimeout(() => {
                        setShowScanner(false);
                    }, 1500);
                }
            }
        } catch (error) {
            setLoading(false);
            setIsVisible(false);
        }
    };
    return (
        <div className='absolute w-full top-[60px] left-0 '>
            <div className="relative">
                {loading && (
                    <div className="absolute top-16 left-0 w-full h-72 flex flex-col gap-4 items-center justify-center bg-white z-10">
                        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900"></div>
                        <p>Marking Attendance..</p>
                    </div>
                )}
                {attendanceSuccess && <div className="absolute top-16 left-0 w-full h-72 flex flex-col gap-4 items-center justify-center bg-white z-10">
                    <p>Attendance Marked</p>
                </div>}
                {attendanceError && <div className="absolute top-16 left-0 w-full h-72 flex flex-col gap-4 items-center justify-center bg-white z-10 p-4">
                    <p>{attendanceError}</p>
                </div>}
                {!attendanceSuccess && <Scanner
                    onResult={(text) => handleAttendance(JSON.parse(text))}
                    onError={(error) => console.log(error?.message)}
                    styles={{ width: "100%" }}
                    enabled={isVisible}
                />}
            </div>
        </div>
    );
}